#!/usr/bin/env python
"""
Test script to verify transaction form functionality
"""
import os
import sys
import django
import time

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Change to src directory for Django setup
os.chdir(src_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.accounts.models import User
from apps.transactions.models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency

def test_data_availability():
    """Test that all required data is available"""
    print("🔍 Testing Data Availability")
    print("=" * 50)
    
    # Check basic data
    customers = Customer.objects.filter(is_deleted=False)
    locations = Location.objects.filter(is_deleted=False)
    currencies = Currency.objects.filter(is_deleted=False)
    transaction_types = TransactionType.objects.filter(is_deleted=False)
    transactions = Transaction.objects.filter(is_deleted=False)
    
    print(f"✅ Customers: {customers.count()}")
    print(f"✅ Locations: {locations.count()}")
    print(f"✅ Currencies: {currencies.count()}")
    print(f"✅ Transaction Types: {transaction_types.count()}")
    print(f"✅ Transactions: {transactions.count()}")
    
    # Check transaction types
    print("\n📋 Transaction Types:")
    for tt in transaction_types:
        count = transactions.filter(transaction_type=tt).count()
        print(f"   {tt.code}: {tt.name} ({count} transactions)")
    
    # Check if we have admin users
    admin_users = User.objects.filter(is_superuser=True, is_deleted=False)
    print(f"\n👤 Admin Users: {admin_users.count()}")
    for user in admin_users:
        print(f"   {user.username} - {user.get_display_name()}")
    
    return {
        'customers': customers.count(),
        'locations': locations.count(),
        'currencies': currencies.count(),
        'transaction_types': transaction_types.count(),
        'transactions': transactions.count(),
        'admin_users': admin_users.count()
    }

def test_transaction_filtering():
    """Test transaction filtering by type"""
    print("\n🔍 Testing Transaction Filtering")
    print("=" * 50)
    
    transaction_types = TransactionType.objects.filter(is_deleted=False)
    
    for tt in transaction_types:
        # Test filtering by transaction type
        filtered_transactions = Transaction.objects.filter(
            transaction_type=tt,
            is_deleted=False
        )
        
        # Test filtering by transaction type code
        code_filtered_transactions = Transaction.objects.filter(
            transaction_type__code=tt.code,
            is_deleted=False
        )
        
        print(f"📊 {tt.code}:")
        print(f"   By type ID: {filtered_transactions.count()} transactions")
        print(f"   By type code: {code_filtered_transactions.count()} transactions")
        
        if filtered_transactions.count() != code_filtered_transactions.count():
            print(f"   ⚠️  Mismatch in filtering results!")

def main():
    print("🚀 Arena Doviz Transaction Forms Test")
    print("=" * 60)
    
    try:
        # Test data availability
        data_stats = test_data_availability()
        
        # Test transaction filtering
        test_transaction_filtering()
        
        print("\n" + "=" * 60)
        print("📊 Summary:")
        
        all_good = True
        required_minimums = {
            'customers': 1,
            'locations': 1,
            'currencies': 1,
            'transaction_types': 1,
            'admin_users': 1
        }
        
        for key, minimum in required_minimums.items():
            if data_stats[key] >= minimum:
                print(f"✅ {key.replace('_', ' ').title()}: {data_stats[key]} (minimum {minimum})")
            else:
                print(f"❌ {key.replace('_', ' ').title()}: {data_stats[key]} (minimum {minimum} required)")
                all_good = False
        
        if all_good:
            print("\n🎉 All data requirements met! Transaction forms should work properly.")
            print("\n💡 If forms are still not loading:")
            print("   1. Check browser console for JavaScript errors")
            print("   2. Verify JWT tokens are being retrieved (check localStorage)")
            print("   3. Test API endpoints directly: /api/v1/customers/customers/")
            print("   4. Ensure user is logged in and has proper permissions")
        else:
            print("\n⚠️  Some data requirements not met. Please check the issues above.")
            
    except Exception as e:
        print(f"❌ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
