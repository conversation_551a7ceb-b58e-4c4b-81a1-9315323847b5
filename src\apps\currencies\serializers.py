"""
Serializers for Arena Doviz Currencies app.
"""

from rest_framework import serializers
from .models import Currency, ExchangeRate, ExchangeRateHistory
from decimal import Decimal
import logging

logger = logging.getLogger(__name__)


class CurrencySerializer(serializers.ModelSerializer):
    """Serializer for Currency model."""
    
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    
    class Meta:
        model = Currency
        fields = [
            'id', 'code', 'name', 'symbol', 'decimal_places', 'is_active',
            'is_base_currency', 'sort_order', 'notes', 'display_name',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate_code(self, value):
        """Validate currency code format."""
        if not value.isupper():
            raise serializers.ValidationError("Currency code must be uppercase")
        
        if len(value) != 3:
            raise serializers.ValidationError("Currency code must be exactly 3 characters")
        
        return value
    
    def validate_decimal_places(self, value):
        """Validate decimal places."""
        if value < 0 or value > 4:
            raise serializers.ValidationError("Decimal places must be between 0 and 4")
        
        return value


class CurrencyListSerializer(serializers.ModelSerializer):
    """Simplified serializer for currency list views."""
    
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    
    class Meta:
        model = Currency
        fields = [
            'id', 'code', 'name', 'symbol', 'decimal_places',
            'is_active', 'is_base_currency', 'display_name', 'sort_order'
        ]


class ExchangeRateHistorySerializer(serializers.ModelSerializer):
    """Serializer for ExchangeRateHistory model."""
    
    changed_by_name = serializers.CharField(source='changed_by.get_display_name', read_only=True)
    buy_rate_change = serializers.DecimalField(source='get_buy_rate_change', max_digits=15, decimal_places=6, read_only=True)
    sell_rate_change = serializers.DecimalField(source='get_sell_rate_change', max_digits=15, decimal_places=6, read_only=True)
    buy_rate_change_percentage = serializers.DecimalField(source='get_buy_rate_change_percentage', max_digits=5, decimal_places=2, read_only=True)
    sell_rate_change_percentage = serializers.DecimalField(source='get_sell_rate_change_percentage', max_digits=5, decimal_places=2, read_only=True)
    
    class Meta:
        model = ExchangeRateHistory
        fields = [
            'id', 'old_buy_rate', 'new_buy_rate', 'old_sell_rate', 'new_sell_rate',
            'changed_by', 'changed_by_name', 'reason', 'buy_rate_change',
            'sell_rate_change', 'buy_rate_change_percentage', 'sell_rate_change_percentage',
            'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ExchangeRateSerializer(serializers.ModelSerializer):
    """Serializer for ExchangeRate model."""
    
    # Currency information
    from_currency_code = serializers.CharField(source='from_currency.code', read_only=True)
    from_currency_name = serializers.CharField(source='from_currency.name', read_only=True)
    from_currency_symbol = serializers.CharField(source='from_currency.symbol', read_only=True)
    to_currency_code = serializers.CharField(source='to_currency.code', read_only=True)
    to_currency_name = serializers.CharField(source='to_currency.name', read_only=True)
    to_currency_symbol = serializers.CharField(source='to_currency.symbol', read_only=True)
    
    # Location information
    location_name = serializers.CharField(source='location.name', read_only=True)
    location_code = serializers.CharField(source='location.code', read_only=True)
    
    # Calculated fields
    spread = serializers.DecimalField(source='get_spread', max_digits=15, decimal_places=6, read_only=True)
    spread_percentage = serializers.DecimalField(source='get_spread_percentage', max_digits=5, decimal_places=2, read_only=True)
    is_current = serializers.BooleanField(read_only=True)
    
    # History
    history = ExchangeRateHistorySerializer(many=True, read_only=True)
    
    class Meta:
        model = ExchangeRate
        fields = [
            'id', 'from_currency', 'from_currency_code', 'from_currency_name', 'from_currency_symbol',
            'to_currency', 'to_currency_code', 'to_currency_name', 'to_currency_symbol',
            'location', 'location_name', 'location_code', 'buy_rate', 'sell_rate',
            'effective_from', 'effective_until', 'is_active', 'source', 'notes',
            'spread', 'spread_percentage', 'is_current', 'history',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def validate(self, attrs):
        """Validate exchange rate data."""
        buy_rate = attrs.get('buy_rate')
        sell_rate = attrs.get('sell_rate')
        
        if buy_rate and sell_rate:
            if sell_rate <= buy_rate:
                raise serializers.ValidationError({
                    'sell_rate': 'Sell rate must be higher than buy rate'
                })
        
        effective_from = attrs.get('effective_from')
        effective_until = attrs.get('effective_until')
        
        if effective_from and effective_until:
            if effective_until <= effective_from:
                raise serializers.ValidationError({
                    'effective_until': 'Effective until date must be after effective from date'
                })
        
        return attrs


class ExchangeRateListSerializer(serializers.ModelSerializer):
    """Simplified serializer for exchange rate list views."""
    
    from_currency_code = serializers.CharField(source='from_currency.code', read_only=True)
    to_currency_code = serializers.CharField(source='to_currency.code', read_only=True)
    location_code = serializers.CharField(source='location.code', read_only=True)
    spread = serializers.DecimalField(source='get_spread', max_digits=15, decimal_places=6, read_only=True)
    is_current = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = ExchangeRate
        fields = [
            'id', 'from_currency_code', 'to_currency_code', 'location_code',
            'buy_rate', 'sell_rate', 'spread', 'effective_from',
            'effective_until', 'is_active', 'is_current'
        ]


class ExchangeRateCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating exchange rates."""
    
    class Meta:
        model = ExchangeRate
        fields = [
            'from_currency', 'to_currency', 'location', 'buy_rate', 'sell_rate',
            'effective_from', 'effective_until', 'is_active', 'source', 'notes'
        ]
    
    def validate(self, attrs):
        """Validate exchange rate data."""
        from_currency = attrs.get('from_currency')
        to_currency = attrs.get('to_currency')
        
        if from_currency == to_currency:
            raise serializers.ValidationError({
                'to_currency': 'From currency and to currency must be different'
            })
        
        buy_rate = attrs.get('buy_rate')
        sell_rate = attrs.get('sell_rate')
        
        if buy_rate and sell_rate:
            if sell_rate <= buy_rate:
                raise serializers.ValidationError({
                    'sell_rate': 'Sell rate must be higher than buy rate'
                })
        
        return attrs


class CurrencyStatsSerializer(serializers.Serializer):
    """Serializer for currency statistics."""
    
    total_currencies = serializers.IntegerField()
    active_currencies = serializers.IntegerField()
    base_currency = serializers.CharField()
    total_exchange_rates = serializers.IntegerField()
    active_exchange_rates = serializers.IntegerField()
    currencies_by_decimal_places = serializers.DictField()
    latest_rate_updates = serializers.IntegerField()


class CurrentRateSerializer(serializers.Serializer):
    """Serializer for current exchange rates."""
    
    from_currency = serializers.CharField()
    to_currency = serializers.CharField()
    location = serializers.CharField()
    buy_rate = serializers.DecimalField(max_digits=15, decimal_places=6)
    sell_rate = serializers.DecimalField(max_digits=15, decimal_places=6)
    spread = serializers.DecimalField(max_digits=15, decimal_places=6)
    spread_percentage = serializers.DecimalField(max_digits=5, decimal_places=2)
    effective_from = serializers.DateTimeField()
    last_updated = serializers.DateTimeField()


class ExchangeCalculationSerializer(serializers.Serializer):
    """Serializer for exchange calculations."""
    
    from_currency = serializers.CharField()
    to_currency = serializers.CharField()
    location = serializers.CharField()
    amount = serializers.DecimalField(max_digits=15, decimal_places=6)
    rate_type = serializers.ChoiceField(choices=['buy', 'sell'], default='sell')
    
    def validate_amount(self, value):
        """Validate amount is positive."""
        if value <= 0:
            raise serializers.ValidationError("Amount must be positive")
        return value


class ExchangeCalculationResultSerializer(serializers.Serializer):
    """Serializer for exchange calculation results."""
    
    from_currency = serializers.CharField()
    to_currency = serializers.CharField()
    location = serializers.CharField()
    from_amount = serializers.DecimalField(max_digits=15, decimal_places=6)
    to_amount = serializers.DecimalField(max_digits=15, decimal_places=6)
    exchange_rate = serializers.DecimalField(max_digits=15, decimal_places=6)
    rate_type = serializers.CharField()
    calculation_time = serializers.DateTimeField()
