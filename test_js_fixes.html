<!DOCTYPE html>
<html>
<head>
    <title>Test JavaScript Fixes</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Testing JavaScript Fixes</h1>
    
    <div id="test-results"></div>
    
    <script>
        // Test the fixed safeGetJQueryValue usage
        function testJavaScriptFixes() {
            const results = [];
            
            // Test 1: Create a mock datalist with options
            $('body').append(`
                <datalist id="test-datalist">
                    <option value="Test Option 1">Test Option 1</option>
                    <option value="Test Option 2">Test Option 2</option>
                    <option value="" disabled>Disabled Option</option>
                </datalist>
            `);
            
            try {
                // Test the fixed logic
                const datalist = $('#test-datalist');
                let optionCount = 0;
                
                datalist.find('option').each(function() {
                    if ($(this).val() && !$(this).prop('disabled')) {
                        optionCount++;
                    }
                });
                
                results.push(`✅ Option counting test passed: Found ${optionCount} valid options`);
                
                // Test courier name search
                let exists = false;
                datalist.find('option').each(function() {
                    if ($(this).val().toLowerCase().includes('test option 1')) {
                        exists = true;
                        return false; // break
                    }
                });
                
                results.push(`✅ Courier search test passed: Found existing courier = ${exists}`);
                
            } catch (error) {
                results.push(`❌ JavaScript error: ${error.message}`);
            }
            
            // Display results
            $('#test-results').html('<ul><li>' + results.join('</li><li>') + '</li></ul>');
        }
        
        // Run tests when page loads
        $(document).ready(function() {
            testJavaScriptFixes();
        });
    </script>
</body>
</html>
