#!/usr/bin/env python
"""
Test PostgreSQL database connection for Arena Doviz
"""
import os
import sys
import psycopg2
from pathlib import Path

def test_postgresql_connection():
    """Test PostgreSQL connection with the configured credentials"""
    
    # Database configuration
    db_config = {
        'host': 'localhost',
        'port': '5432',
        'user': 'postgres',
        'password': 'Pamir2600!',
        'database': 'postgres'  # Connect to default postgres database first
    }
    
    print("🔍 Testing PostgreSQL Connection...")
    print(f"Host: {db_config['host']}")
    print(f"Port: {db_config['port']}")
    print(f"User: {db_config['user']}")
    print()
    
    try:
        # Test basic connection
        print("1. Testing basic PostgreSQL connection...")
        conn = psycopg2.connect(**db_config)
        cursor = conn.cursor()
        
        # Test query
        cursor.execute("SELECT version();")
        version = cursor.fetchone()[0]
        print(f"✅ PostgreSQL connection successful!")
        print(f"   Version: {version}")
        
        # Check if arena_doviz_prod database exists
        print("\n2. Checking if arena_doviz_prod database exists...")
        cursor.execute("SELECT 1 FROM pg_database WHERE datname='arena_doviz_prod';")
        db_exists = cursor.fetchone()
        
        if db_exists:
            print("✅ Database 'arena_doviz_prod' exists")
            
            # Test connection to the actual database
            cursor.close()
            conn.close()
            
            print("\n3. Testing connection to arena_doviz_prod database...")
            db_config['database'] = 'arena_doviz_prod'
            conn = psycopg2.connect(**db_config)
            cursor = conn.cursor()
            
            # Test a simple query
            cursor.execute("SELECT current_database(), current_user;")
            db_name, user = cursor.fetchone()
            print(f"✅ Connected to database: {db_name} as user: {user}")
            
        else:
            print("⚠️  Database 'arena_doviz_prod' does not exist")
            print("   It will be created when you run the server")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 All database tests passed!")
        return True
        
    except psycopg2.OperationalError as e:
        print(f"❌ PostgreSQL connection failed: {e}")
        print("\nPossible solutions:")
        print("1. Make sure PostgreSQL service is running")
        print("2. Check if the password 'Pamir2600!' is correct")
        print("3. Verify PostgreSQL is installed at C:\\Program Files\\PostgreSQL\\17\\bin")
        print("4. Check if PostgreSQL is listening on localhost:5432")
        return False
        
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        return False

def test_django_database_config():
    """Test Django database configuration"""
    print("\n" + "="*50)
    print("🔧 Testing Django Database Configuration")
    print("="*50)
    
    # Set environment variables
    os.environ['DB_NAME'] = 'arena_doviz_prod'
    os.environ['DB_USER'] = 'postgres'
    os.environ['DB_PASSWORD'] = 'Pamir2600!'
    os.environ['DB_HOST'] = 'localhost'
    os.environ['DB_PORT'] = '5432'
    os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.prod'
    os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='
    
    # Add src directory to Python path
    src_path = Path(__file__).parent / 'src'
    sys.path.insert(0, str(src_path))
    
    try:
        # Import Django settings
        import django
        from django.conf import settings
        django.setup()
        
        print("✅ Django settings loaded successfully")
        print(f"   Settings module: {settings.SETTINGS_MODULE}")
        
        # Check database configuration
        db_config = settings.DATABASES['default']
        print(f"✅ Database engine: {db_config['ENGINE']}")
        print(f"   Database name: {db_config['NAME']}")
        print(f"   Database user: {db_config['USER']}")
        print(f"   Database host: {db_config['HOST']}")
        print(f"   Database port: {db_config['PORT']}")
        
        # Test Django database connection
        from django.db import connection
        print("\n4. Testing Django database connection...")
        
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            result = cursor.fetchone()
            
        print("✅ Django database connection successful!")
        return True
        
    except Exception as e:
        print(f"❌ Django database configuration error: {e}")
        return False

def main():
    """Main function"""
    print("Arena Doviz Database Connection Test")
    print("="*50)
    
    # Test PostgreSQL connection
    pg_success = test_postgresql_connection()
    
    if pg_success:
        # Test Django configuration
        django_success = test_django_database_config()
        
        if django_success:
            print("\n🎉 All tests passed! You can now run the server.")
            print("\nTo start the server, run:")
            print("   run_server.bat")
            return True
    
    print("\n❌ Database connection tests failed!")
    print("Please fix the issues above before running the server.")
    return False

if __name__ == '__main__':
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
