#!/usr/bin/env python
"""
Simple API test without Django setup
"""
import requests
import json

def test_api_endpoints():
    """Test API endpoints with a simple token"""
    base_url = "http://localhost:8000/api/v1"
    
    # Try to get a token first by checking if we can access without auth
    print("🔍 Testing Arena Doviz API Endpoints")
    print("=" * 50)
    
    # Test endpoints without authentication first
    endpoints = [
        (f"{base_url}/customers/customers/", "Customers API"),
        (f"{base_url}/locations/locations/", "Locations API"),
        (f"{base_url}/currencies/currencies/", "Currencies API"),
        (f"{base_url}/transactions/types/", "Transaction Types API"),
        (f"{base_url}/transactions/transactions/", "Transactions API"),
    ]
    
    for url, description in endpoints:
        try:
            response = requests.get(url, timeout=10)
            print(f"📡 {description}: HTTP {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    count = len(data.get('results', []))
                    print(f"   ✅ Success: {count} items")
                except:
                    print(f"   ✅ Success: Response received")
            elif response.status_code == 401:
                print(f"   🔐 Authentication required")
            elif response.status_code == 403:
                print(f"   🚫 Forbidden")
            else:
                print(f"   ❌ Error: {response.text[:100]}")
                
        except Exception as e:
            print(f"   ❌ Connection error: {e}")
    
    print("\n" + "=" * 50)
    print("📊 Testing transaction type filtering:")
    
    # Test specific transaction type filtering
    test_codes = ['EXCHANGE', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL']
    for code in test_codes:
        try:
            url = f"{base_url}/transactions/transactions/?transaction_type_code={code}"
            response = requests.get(url, timeout=10)
            print(f"📡 {code} transactions: HTTP {response.status_code}")
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    count = len(data.get('results', []))
                    print(f"   ✅ Found: {count} {code} transactions")
                except:
                    print(f"   ✅ Response received")
            elif response.status_code == 401:
                print(f"   🔐 Authentication required")
            else:
                print(f"   ❌ Error")
                
        except Exception as e:
            print(f"   ❌ Connection error: {e}")

if __name__ == "__main__":
    test_api_endpoints()
