@echo off
echo Starting Arena Doviz Production Server...
echo.

REM Set the encryption key
set ARENA_ENCRYPTION_KEY=x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo=

REM Temporarily use SQLite fallback with your fixes
REM set DB_NAME=arena_doviz_prod
REM set DB_USER=postgres
REM set DB_PASSWORD=Pamir2600!
REM set DB_HOST=localhost
REM set DB_PORT=5432

REM Temporarily skip PostgreSQL setup - using SQLite fallback
echo Using SQLite database with your fixes...

REM Change to src directory
cd /d "%~dp0src"

REM Run migrations
echo Running database migrations...
python manage.py migrate --settings=config.settings.prod
if %ERRORLEVEL% neq 0 (
    echo ERROR: Database migration failed
    pause
    exit /b 1
)

REM Collect static files
echo Collecting static files...
python manage.py collectstatic --noinput --settings=config.settings.prod

REM Run the Django development server with production settings
echo.
echo Running server at http://0.0.0.0:8000
echo Press Ctrl+C to stop the server
echo.

python manage.py runserver 0.0.0.0:8000 --settings=config.settings.prod

pause
