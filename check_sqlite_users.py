#!/usr/bin/env python
"""
Check users in SQLite database
"""
import os
import sys
import django
from pathlib import Path

# Set environment variables (no DB_PASSWORD to trigger SQLite fallback)
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.prod'
os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Add src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group

def check_users():
    """Check existing users in SQLite database"""
    User = get_user_model()
    
    print("🔍 Checking SQLite Database Users...")
    print("="*50)
    
    # List all users
    users = User.objects.all()
    print(f"Total users found: {users.count()}")
    print()
    
    if users.exists():
        print("📋 User Details:")
        for user in users:
            groups = ', '.join([g.name for g in user.groups.all()])
            print(f"- Username: {user.username}")
            print(f"  Email: {user.email}")
            print(f"  Active: {user.is_active}")
            print(f"  Staff: {user.is_staff}")
            print(f"  Superuser: {user.is_superuser}")
            print(f"  Groups: {groups}")
            print(f"  Last login: {user.last_login}")
            print()
    else:
        print("❌ No users found in database")
    
    # Check groups
    print("👥 Available Groups:")
    groups = Group.objects.all()
    for group in groups:
        user_count = group.user_set.count()
        print(f"- {group.name} ({user_count} users)")
    
    return users.exists()

def check_database_info():
    """Check database information"""
    from django.conf import settings
    from django.db import connection
    
    print("\n📊 Database Information:")
    print("="*30)
    
    db_config = settings.DATABASES['default']
    print(f"Engine: {db_config['ENGINE']}")
    print(f"Database: {db_config['NAME']}")
    
    # Check if it's SQLite
    if 'sqlite' in db_config['ENGINE']:
        db_path = Path(db_config['NAME'])
        if db_path.exists():
            size = db_path.stat().st_size
            print(f"File size: {size:,} bytes")
            print(f"File path: {db_path}")
        else:
            print("❌ Database file does not exist")
    
    # Check table count
    with connection.cursor() as cursor:
        cursor.execute("SELECT name FROM sqlite_master WHERE type='table';")
        tables = cursor.fetchall()
        print(f"Tables: {len(tables)}")

def main():
    """Main function"""
    print("Arena Doviz SQLite Database Check")
    print("="*50)
    
    check_database_info()
    users_exist = check_users()
    
    if users_exist:
        print("✅ Users found in SQLite database!")
        print("\n🚀 You should be able to login with existing credentials")
    else:
        print("❌ No users found!")
        print("\n💡 You may need to create users or check if you're using the right database file")
    
    return users_exist

if __name__ == '__main__':
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
