{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Customer Details" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-person"></i>
                {% trans "Customer Details" %}
            </h1>
            <div class="btn-group" role="group">
                <a href="{% url 'customers_web:list' %}" class="btn btn-outline-secondary">
                    <i class="bi bi-arrow-left"></i>
                    {% trans "Back to List" %}
                </a>
                <button class="btn btn-primary" id="edit-customer">
                    <i class="bi bi-pencil"></i>
                    {% trans "Edit Customer" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Customer Information -->
<div class="row mb-4">
    <div class="col-md-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-info-circle"></i>
                    {% trans "Customer Information" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Customer Code" %}</label>
                            <p class="mb-0" id="customer-code">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Full Name" %}</label>
                            <p class="mb-0" id="customer-name">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Customer Type" %}</label>
                            <p class="mb-0" id="customer-type">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Status" %}</label>
                            <p class="mb-0" id="customer-status">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Phone Number" %}</label>
                            <p class="mb-0" id="customer-phone">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Email" %}</label>
                            <p class="mb-0" id="customer-email">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Registration Date" %}</label>
                            <p class="mb-0" id="registration-date">-</p>
                        </div>
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Preferred Location" %}</label>
                            <p class="mb-0" id="preferred-location">-</p>
                        </div>
                    </div>
                </div>
                <div class="row" id="company-info" style="display: none;">
                    <div class="col-12">
                        <hr>
                        <h6>{% trans "Company Information" %}</h6>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Company Name" %}</label>
                            <p class="mb-0" id="company-name">-</p>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Tax Number" %}</label>
                            <p class="mb-0" id="tax-number">-</p>
                        </div>
                    </div>
                </div>
                <div class="row" id="notes-section">
                    <div class="col-12">
                        <div class="mb-3">
                            <label class="form-label fw-bold">{% trans "Notes" %}</label>
                            <p class="mb-0" id="customer-notes">-</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <!-- Balance Summary -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-wallet"></i>
                    {% trans "Balance Summary" %}
                </h5>
            </div>
            <div class="card-body">
                <div id="balance-summary">
                    <div class="text-center text-muted">
                        <div class="spinner-border spinner-border-sm" role="status">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-2">{% trans "Loading balance..." %}</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Actions -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-lightning"></i>
                    {% trans "Quick Actions" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <div class="dropdown">
                        <button class="btn btn-primary dropdown-toggle w-100" type="button" id="quickActionsDropdown" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-plus-circle"></i>
                            {% trans "New Transaction" %}
                        </button>
                        <ul class="dropdown-menu w-100" aria-labelledby="quickActionsDropdown">
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('DEPOSIT')">
                                    <i class="bi bi-cash-coin text-success"></i>
                                    {% trans "Cash Deposit" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('WITHDRAWAL')">
                                    <i class="bi bi-cash-stack text-danger"></i>
                                    {% trans "Cash Withdrawal" %}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('EXCHANGE')">
                                    <i class="bi bi-arrow-left-right text-primary"></i>
                                    {% trans "Currency Exchange" %}
                                </a>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('INTERNAL_TRANSFER')">
                                    <i class="bi bi-arrow-right text-info"></i>
                                    {% trans "Internal Transfer" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('EXTERNAL_TRANSFER')">
                                    <i class="bi bi-send text-warning"></i>
                                    {% trans "External Transfer" %}
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="#" onclick="startQuickTransaction('INTERNATIONAL_TRANSFER')">
                                    <i class="bi bi-globe text-secondary"></i>
                                    {% trans "International Transfer" %}
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Quick Stats -->
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-graph-up"></i>
                    {% trans "Quick Stats" %}
                </h5>
            </div>
            <div class="card-body">
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="mb-1" id="total-transactions">-</h4>
                            <small class="text-muted">{% trans "Total Transactions" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="mb-1" id="total-volume">-</h4>
                        <small class="text-muted">{% trans "Total Volume" %}</small>
                    </div>
                </div>
                <hr>
                <div class="row text-center">
                    <div class="col-6">
                        <div class="border-end">
                            <h4 class="mb-1" id="last-transaction">-</h4>
                            <small class="text-muted">{% trans "Last Transaction" %}</small>
                        </div>
                    </div>
                    <div class="col-6">
                        <h4 class="mb-1" id="avg-transaction">-</h4>
                        <small class="text-muted">{% trans "Avg Transaction" %}</small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Recent Transactions -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-clock-history"></i>
                    {% trans "Recent Transactions" %}
                </h5>
                <a href="/transactions/?customer={{ customer_id }}" class="btn btn-sm btn-outline-primary">
                    {% trans "View All Transactions" %}
                </a>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="recent-transactions-table">
                        <thead>
                            <tr>
                                <th>{% trans "Transaction #" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Amount" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Date" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td colspan="6" class="text-center text-muted">
                                    {% trans "Loading..." %}
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Contacts -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-telephone"></i>
                    {% trans "Contact Information" %}
                </h5>
                <button class="btn btn-sm btn-outline-primary" id="add-contact">
                    <i class="bi bi-plus"></i>
                    {% trans "Add Contact" %}
                </button>
            </div>
            <div class="card-body">
                <div id="contacts-list">
                    <div class="text-center text-muted">
                        {% trans "Loading contacts..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Customer Documents -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-file-earmark"></i>
                    {% trans "Documents" %}
                </h5>
                <button class="btn btn-sm btn-outline-primary" id="upload-document">
                    <i class="bi bi-upload"></i>
                    {% trans "Upload Document" %}
                </button>
            </div>
            <div class="card-body">
                <div id="documents-list">
                    <div class="text-center text-muted">
                        {% trans "Loading documents..." %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Add Contact Modal -->
<div class="modal fade" id="contactModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Add Contact" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="contact-form">
                    <div class="mb-3">
                        <label for="contact-type" class="form-label">{% trans "Contact Type" %} *</label>
                        <select class="form-select" id="contact-type" name="contact_type" required>
                            <option value="">{% trans "Select type..." %}</option>
                            <option value="phone">{% trans "Phone" %}</option>
                            <option value="email">{% trans "Email" %}</option>
                            <option value="address">{% trans "Address" %}</option>
                            <option value="emergency">{% trans "Emergency Contact" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="contact-value" class="form-label">{% trans "Contact Value" %} *</label>
                        <input type="text" class="form-control" id="contact-value" name="contact_value" required>
                    </div>
                    <div class="mb-3">
                        <label for="contact-label" class="form-label">{% trans "Label" %}</label>
                        <input type="text" class="form-control" id="contact-label" name="label" placeholder="{% trans 'e.g., Home, Work, Mobile...' %}">
                    </div>
                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="is-primary" name="is_primary">
                            <label class="form-check-label" for="is-primary">
                                {% trans "Primary Contact" %}
                            </label>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label for="contact-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="contact-notes" name="notes" rows="2"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-contact">{% trans "Save Contact" %}</button>
            </div>
        </div>
    </div>
</div>

<!-- Upload Document Modal -->
<div class="modal fade" id="documentModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{% trans "Upload Document" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="document-form" enctype="multipart/form-data">
                    <div class="mb-3">
                        <label for="document-file" class="form-label">{% trans "Select File" %} *</label>
                        <input type="file" class="form-control" id="document-file" name="file" required accept=".pdf,.jpg,.jpeg,.png,.doc,.docx,.xls,.xlsx">
                        <div class="form-text">{% trans "Supported formats: PDF, JPG, PNG, DOC, DOCX, XLS, XLSX. Max 10MB." %}</div>
                    </div>
                    <div class="mb-3">
                        <label for="document-type" class="form-label">{% trans "Document Type" %} *</label>
                        <select class="form-select" id="document-type" name="document_type" required>
                            <option value="">{% trans "Select type..." %}</option>
                            <option value="id_copy">{% trans "ID Copy" %}</option>
                            <option value="passport">{% trans "Passport" %}</option>
                            <option value="proof_of_address">{% trans "Proof of Address" %}</option>
                            <option value="bank_statement">{% trans "Bank Statement" %}</option>
                            <option value="contract">{% trans "Contract" %}</option>
                            <option value="receipt">{% trans "Receipt" %}</option>
                            <option value="other">{% trans "Other" %}</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="document-title" class="form-label">{% trans "Document Title" %}</label>
                        <input type="text" class="form-control" id="document-title" name="title" placeholder="{% trans 'Document title...' %}">
                    </div>
                    <div class="mb-3">
                        <label for="document-description" class="form-label">{% trans "Description" %}</label>
                        <textarea class="form-control" id="document-description" name="description" rows="2" placeholder="{% trans 'Document description...' %}"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-document">{% trans "Upload Document" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let customerId = null;

$(document).ready(function() {
    // Get customer ID from URL
    const pathParts = window.location.pathname.split('/');
    customerId = pathParts[pathParts.length - 2]; // Get UUID from URL
    
    if (customerId) {
        loadCustomerDetails();
        loadCustomerBalance();
        loadCustomerStats();
        loadRecentTransactions();
        loadCustomerContacts();
        loadCustomerDocuments();
    }
    
    // Event handlers
    $('#edit-customer').on('click', function() {
        window.location.href = `/customers/${customerId}/edit/`;
    });

    $('#add-contact').on('click', function() {
        $('#contact-form')[0].reset();
        $('#contactModal').modal('show');
    });

    $('#upload-document').on('click', function() {
        $('#document-form')[0].reset();
        $('#documentModal').modal('show');
    });

    $('#save-contact').on('click', function() {
        saveContact();
    });

    $('#save-document').on('click', function() {
        saveDocument();
    });
});

// Quick Actions functionality
function startQuickTransaction(transactionType) {
    if (!customerId) {
        showAlert('danger', '{% trans "Customer ID not found" %}');
        return;
    }

    let url = '';

    switch(transactionType) {
        case 'DEPOSIT':
            url = `/transactions/type/DEPOSIT/add/?customer=${customerId}`;
            break;
        case 'WITHDRAWAL':
            url = `/transactions/type/WITHDRAWAL/add/?customer=${customerId}`;
            break;
        case 'EXCHANGE':
            url = `/transactions/type/EXCHANGE/add/?customer=${customerId}`;
            break;
        case 'INTERNAL_TRANSFER':
            url = `/transactions/transfer/internal/add/?customer=${customerId}`;
            break;
        case 'EXTERNAL_TRANSFER':
            url = `/transactions/transfer/external/add/?customer=${customerId}`;
            break;
        case 'INTERNATIONAL_TRANSFER':
            url = `/transactions/transfer/international/add/?customer=${customerId}`;
            break;
        default:
            showAlert('danger', '{% trans "Unknown transaction type" %}');
            return;
    }

    // Redirect to the transaction form with customer pre-populated
    window.location.href = url;
}

function loadCustomerDetails() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            populateCustomerDetails(data);
        },
        error: function(xhr) {
            console.error('Error loading customer details:', xhr);
            showAlert('danger', '{% trans "Failed to load customer details" %}');
        }
    });
}

function populateCustomerDetails(customer) {
    $('#customer-code').text(customer.customer_code || '-');
    $('#customer-name').text(customer.display_name || '-');
    $('#customer-type').html(`<span class="badge bg-${customer.customer_type === 'individual' ? 'primary' : 'secondary'}">${customer.customer_type_display}</span>`);
    $('#customer-status').html(`<span class="badge bg-${getStatusClass(customer.status)}">${customer.status_display}</span>`);
    $('#customer-phone').text(customer.phone_number || '-');
    $('#customer-email').text(customer.email || '-');
    $('#registration-date').text(customer.registration_date ? new Date(customer.registration_date).toLocaleDateString() : '-');
    $('#preferred-location').text(customer.preferred_location_name || '-');
    $('#customer-notes').text(customer.notes || '-');
    
    // Show company info if corporate customer
    if (customer.customer_type === 'corporate') {
        $('#company-info').show();
        $('#company-name').text(customer.company_name || '-');
        $('#tax-number').text(customer.tax_number || '-');
    }
}

function loadCustomerBalance() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/balance/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayBalanceSummary(data);
        },
        error: function(xhr) {
            $('#balance-summary').html('<div class="text-center text-danger">{% trans "Failed to load balance" %}</div>');
        }
    });
}

function displayBalanceSummary(balances) {
    let html = '';
    
    if (balances && balances.length > 0) {
        balances.forEach(function(balance) {
            const balanceClass = parseFloat(balance.balance) >= 0 ? 'text-success' : 'text-danger';
            html += `
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span class="fw-bold">${balance.currency_code}</span>
                    <span class="${balanceClass} fw-bold">${balance.formatted_balance}</span>
                </div>
            `;
        });
    } else {
        html = '<div class="text-center text-muted">{% trans "No balance information available" %}</div>';
    }
    
    $('#balance-summary').html(html);
}

function loadCustomerStats() {
    $.ajax({
        url: `/api/v1/customers/customers/${customerId}/stats/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#total-transactions').text(data.total_transactions || '0');
            $('#total-volume').text(data.total_volume || '$0.00');
            $('#last-transaction').text(data.last_transaction_date ? new Date(data.last_transaction_date).toLocaleDateString() : '-');
            $('#avg-transaction').text(data.avg_transaction_amount || '$0.00');
        },
        error: function(xhr) {
            console.error('Error loading customer stats:', xhr);
        }
    });
}

function loadRecentTransactions() {
    $.ajax({
        url: `/api/v1/transactions/transactions/?customer=${customerId}&limit=10`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayRecentTransactions(data.results || []);
        },
        error: function(xhr) {
            $('#recent-transactions-table tbody').html('<tr><td colspan="6" class="text-center text-danger">{% trans "Failed to load transactions" %}</td></tr>');
        }
    });
}

function displayRecentTransactions(transactions) {
    const tbody = $('#recent-transactions-table tbody');
    tbody.empty();
    
    if (transactions.length > 0) {
        transactions.forEach(function(tx) {
            const statusClass = getStatusClass(tx.status);
            const row = `
                <tr>
                    <td><code>${tx.transaction_number}</code></td>
                    <td>${tx.transaction_type_name}</td>
                    <td>${tx.display_amount}</td>
                    <td><span class="badge bg-${statusClass}">${tx.status_display}</span></td>
                    <td>${new Date(tx.created_at).toLocaleDateString()}</td>
                    <td>
                        <a href="/transactions/${tx.id}/" class="btn btn-sm btn-outline-primary">
                            <i class="bi bi-eye"></i>
                        </a>
                    </td>
                </tr>
            `;
            tbody.append(row);
        });
    } else {
        tbody.append('<tr><td colspan="6" class="text-center text-muted">{% trans "No recent transactions found" %}</td></tr>');
    }
}

function loadCustomerContacts() {
    $.ajax({
        url: `/api/v1/customers/contacts/?customer=${customerId}`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayContacts(data.results || []);
        },
        error: function(xhr) {
            $('#contacts-list').html('<div class="text-center text-danger">{% trans "Failed to load contacts" %}</div>');
        }
    });
}

function displayContacts(contacts) {
    let html = '';
    
    if (contacts.length > 0) {
        contacts.forEach(function(contact) {
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between">
                        <div>
                            <strong>${contact.contact_type_display}</strong>
                            <p class="mb-0">${contact.value}</p>
                            ${contact.notes ? `<small class="text-muted">${contact.notes}</small>` : ''}
                        </div>
                        <div>
                            <span class="badge bg-${contact.is_primary ? 'primary' : 'secondary'}">${contact.is_primary ? '{% trans "Primary" %}' : '{% trans "Secondary" %}'}</span>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<div class="text-center text-muted">{% trans "No contacts found" %}</div>';
    }
    
    $('#contacts-list').html(html);
}

function loadCustomerDocuments() {
    $.ajax({
        url: `/api/v1/customers/documents/?customer=${customerId}`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            displayDocuments(data.results || []);
        },
        error: function(xhr) {
            $('#documents-list').html('<div class="text-center text-danger">{% trans "Failed to load documents" %}</div>');
        }
    });
}

function displayDocuments(documents) {
    let html = '';
    
    if (documents.length > 0) {
        documents.forEach(function(doc) {
            html += `
                <div class="border-bottom pb-2 mb-2">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <strong>${doc.document_type_display}</strong>
                            <p class="mb-0">${doc.title}</p>
                            <small class="text-muted">{% trans "Uploaded" %}: ${new Date(doc.created_at).toLocaleDateString()}</small>
                        </div>
                        <div>
                            <a href="${doc.file_url}" class="btn btn-sm btn-outline-primary" target="_blank">
                                <i class="bi bi-download"></i>
                            </a>
                        </div>
                    </div>
                </div>
            `;
        });
    } else {
        html = '<div class="text-center text-muted">{% trans "No documents found" %}</div>';
    }
    
    $('#documents-list').html(html);
}

function getStatusClass(status) {
    const statusClasses = {
        'active': 'success',
        'inactive': 'secondary',
        'suspended': 'warning',
        'blocked': 'danger',
        'draft': 'secondary',
        'pending': 'warning',
        'approved': 'info',
        'completed': 'success',
        'cancelled': 'danger',
        'rejected': 'danger'
    };
    return statusClasses[status] || 'secondary';
}

function saveContact() {
    const formData = new FormData($('#contact-form')[0]);
    const contactData = {};

    for (let [key, value] of formData.entries()) {
        if (key === 'is_primary') {
            contactData[key] = $('#is-primary').is(':checked');
        } else if (value) {
            contactData[key] = value;
        }
    }

    // Add customer ID
    contactData.customer = customerId;

    $.ajax({
        url: '/api/v1/customers/contacts/',
        method: 'POST',
        headers: getAuthHeaders(),
        contentType: 'application/json',
        data: JSON.stringify(contactData),
        success: function(response) {
            $('#contactModal').modal('hide');
            loadCustomerContacts();
            showAlert('success', '{% trans "Contact added successfully" %}');
        },
        error: function(xhr) {
            console.error('Error saving contact:', xhr);
            let errorMessage = '{% trans "Error saving contact" %}';
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
            }
            showAlert('danger', errorMessage);
        }
    });
}

function saveDocument() {
    const formData = new FormData($('#document-form')[0]);

    // Add customer ID
    formData.append('customer', customerId);

    $.ajax({
        url: '/api/v1/customers/documents/',
        method: 'POST',
        headers: getAuthHeaders(),
        data: formData,
        processData: false,
        contentType: false,
        success: function(response) {
            $('#documentModal').modal('hide');
            loadCustomerDocuments();
            showAlert('success', '{% trans "Document uploaded successfully" %}');
        },
        error: function(xhr) {
            console.error('Error uploading document:', xhr);
            let errorMessage = '{% trans "Error uploading document" %}';
            if (xhr.responseJSON && xhr.responseJSON.errors) {
                errorMessage = Object.values(xhr.responseJSON.errors).flat().join(', ');
            }
            showAlert('danger', errorMessage);
        }
    });
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken() || '';
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
