"""
Serializers for Arena Doviz Locations app.
"""

from rest_framework import serializers
from .models import Location, LocationSettings
import logging

logger = logging.getLogger(__name__)


class LocationSettingsSerializer(serializers.ModelSerializer):
    """Serializer for LocationSettings model."""
    
    allowed_ips = serializers.SerializerMethodField()
    
    class Meta:
        model = LocationSettings
        fields = [
            'id', 'whatsapp_enabled', 'whatsapp_group_id', 'email_notifications',
            'sms_notifications', 'require_approval_above', 'auto_approve_below',
            'ip_whitelist', 'allowed_ips', 'session_timeout', 'backup_enabled',
            'backup_frequency', 'additional_settings', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_allowed_ips(self, obj):
        """Get list of allowed IP addresses."""
        return obj.get_allowed_ips()


class LocationSerializer(serializers.ModelSerializer):
    """Serializer for Location model."""
    
    # Related fields
    settings = LocationSettingsSerializer(read_only=True)
    
    # Display fields
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    full_address = serializers.CharField(source='get_full_address', read_only=True)
    current_time = serializers.SerializerMethodField()
    is_operating_now = serializers.BooleanField(read_only=True)
    
    # Manager information
    manager_name = serializers.CharField(source='manager.get_display_name', read_only=True)
    
    # Statistics
    balance_summary = serializers.SerializerMethodField()
    active_users_count = serializers.SerializerMethodField()
    
    class Meta:
        model = Location
        fields = [
            'id', 'name', 'code', 'country', 'city', 'address', 'phone_number',
            'email', 'timezone', 'is_active', 'is_main_office', 'manager',
            'manager_name', 'commission_rate', 'min_transaction_amount',
            'max_transaction_amount', 'opening_time', 'closing_time',
            'notes', 'sort_order', 'display_name', 'full_address',
            'current_time', 'is_operating_now', 'balance_summary',
            'active_users_count', 'settings', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']
    
    def get_current_time(self, obj):
        """Get current time in location's timezone."""
        try:
            current_time = obj.get_current_time()
            return current_time.isoformat() if current_time else None
        except Exception as e:
            logger.error(f"Error getting current time for location {obj.code}: {e}")
            return None
    
    def get_balance_summary(self, obj):
        """Get location balance summary."""
        try:
            return obj.get_balances_summary()
        except Exception as e:
            logger.error(f"Error getting balance summary for location {obj.code}: {e}")
            return {}
    
    def get_active_users_count(self, obj):
        """Get active users count for location."""
        try:
            return obj.get_active_users_count()
        except Exception as e:
            logger.error(f"Error getting active users count for location {obj.code}: {e}")
            return 0
    
    def validate_code(self, value):
        """Validate location code format."""
        if not value.isupper():
            raise serializers.ValidationError("Location code must be uppercase")
        
        if len(value) < 2 or len(value) > 10:
            raise serializers.ValidationError("Location code must be 2-10 characters long")
        
        return value
    
    def validate(self, attrs):
        """Validate location data."""
        # Validate operating hours
        opening_time = attrs.get('opening_time')
        closing_time = attrs.get('closing_time')
        
        if opening_time and closing_time:
            if opening_time == closing_time:
                raise serializers.ValidationError({
                    'closing_time': 'Closing time must be different from opening time'
                })
        
        # Validate transaction amounts
        min_amount = attrs.get('min_transaction_amount')
        max_amount = attrs.get('max_transaction_amount')
        
        if min_amount and max_amount:
            if min_amount >= max_amount:
                raise serializers.ValidationError({
                    'max_transaction_amount': 'Maximum amount must be greater than minimum amount'
                })
        
        return attrs


class LocationListSerializer(serializers.ModelSerializer):
    """Simplified serializer for location list views."""
    
    display_name = serializers.CharField(source='get_display_name', read_only=True)
    manager_name = serializers.CharField(source='manager.get_display_name', read_only=True)
    is_operating_now = serializers.BooleanField(read_only=True)
    
    class Meta:
        model = Location
        fields = [
            'id', 'name', 'code', 'country', 'city', 'display_name',
            'is_active', 'is_main_office', 'manager_name', 'timezone',
            'is_operating_now', 'sort_order', 'commission_rate'
        ]


class LocationCreateSerializer(serializers.ModelSerializer):
    """Serializer for creating locations."""
    
    class Meta:
        model = Location
        fields = [
            'name', 'code', 'country', 'city', 'address', 'phone_number',
            'email', 'timezone', 'is_active', 'is_main_office', 'manager',
            'commission_rate', 'min_transaction_amount', 'max_transaction_amount',
            'opening_time', 'closing_time', 'notes', 'sort_order'
        ]
    
    def validate_code(self, value):
        """Validate location code format."""
        if not value.isupper():
            raise serializers.ValidationError("Location code must be uppercase")
        
        if len(value) < 2 or len(value) > 10:
            raise serializers.ValidationError("Location code must be 2-10 characters long")
        
        return value


class LocationStatsSerializer(serializers.Serializer):
    """Serializer for location statistics."""
    
    total_locations = serializers.IntegerField()
    active_locations = serializers.IntegerField()
    locations_by_country = serializers.DictField()
    main_office = serializers.CharField()
    operating_locations = serializers.IntegerField()
    total_users = serializers.IntegerField()
    total_transactions_today = serializers.IntegerField()


class LocationBalanceSerializer(serializers.Serializer):
    """Serializer for location balance information."""
    
    location_id = serializers.UUIDField()
    location_name = serializers.CharField()
    location_code = serializers.CharField()
    currency_code = serializers.CharField()
    balance = serializers.DecimalField(max_digits=15, decimal_places=6)
    formatted_balance = serializers.CharField()
    last_updated = serializers.DateTimeField()
