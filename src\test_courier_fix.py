#!/usr/bin/env python
"""
Test script to verify the courier field validation fix.
This script tests the transaction creation with different courier field scenarios.
"""

import os
import sys
import django
from decimal import Decimal

# Add the current directory to Python path (assuming we're running from src)
sys.path.insert(0, os.path.dirname(__file__))

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arena_doviz.settings')
django.setup()

from apps.transactions.serializers import TransactionCreateSerializer
from apps.transactions.models import TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
from apps.accounts.models import User

def test_courier_validation():
    """Test courier field validation scenarios."""
    print("🧪 Testing Courier Field Validation")
    print("=" * 50)
    
    try:
        # Get required objects
        transaction_type = TransactionType.objects.filter(code='DEPOSIT').first()
        customer = Customer.objects.first()
        location = Location.objects.first()
        currency = Currency.objects.filter(code='USD').first()
        
        if not all([transaction_type, customer, location, currency]):
            print("❌ Missing required test data")
            return False
        
        print(f"✅ Test data loaded:")
        print(f"   - Transaction Type: {transaction_type}")
        print(f"   - Customer: {customer}")
        print(f"   - Location: {location}")
        print(f"   - Currency: {currency}")
        print()
        
        # Test Case 1: Cash delivery (no courier required)
        print("📋 Test Case 1: Cash delivery method (no courier)")
        data1 = {
            'transaction_type': transaction_type.id,
            'customer': customer.id,
            'location': location.id,
            'from_currency': currency.id,
            'to_currency': currency.id,
            'from_amount': Decimal('100.00'),
            'to_amount': Decimal('100.00'),
            'exchange_rate': Decimal('1.0'),
            'delivery_method': 'cash',
            'status': 'pending'
        }
        
        serializer1 = TransactionCreateSerializer(data=data1)
        if serializer1.is_valid():
            print("✅ Cash delivery validation passed")
        else:
            print(f"❌ Cash delivery validation failed: {serializer1.errors}")
        print()
        
        # Test Case 2: Cash delivery with invalid courier field (should be ignored)
        print("📋 Test Case 2: Cash delivery with invalid courier (should be ignored)")
        data2 = data1.copy()
        data2['courier'] = 'test'  # This should be ignored since delivery_method is not 'courier'
        
        serializer2 = TransactionCreateSerializer(data=data2)
        if serializer2.is_valid():
            print("✅ Cash delivery with invalid courier validation passed (courier ignored)")
        else:
            print(f"❌ Cash delivery with invalid courier validation failed: {serializer2.errors}")
        print()
        
        # Test Case 3: Courier delivery without courier (should fail)
        print("📋 Test Case 3: Courier delivery without courier (should fail)")
        data3 = data1.copy()
        data3['delivery_method'] = 'courier'
        
        serializer3 = TransactionCreateSerializer(data=data3)
        if not serializer3.is_valid() and 'courier' in serializer3.errors:
            print("✅ Courier delivery without courier validation failed as expected")
        else:
            print(f"❌ Courier delivery without courier should have failed: {serializer3.errors}")
        print()
        
        # Test Case 4: Courier delivery with customer courier name
        print("📋 Test Case 4: Courier delivery with customer courier name")
        data4 = data1.copy()
        data4['delivery_method'] = 'courier'
        data4['courier_name'] = 'John Doe'
        data4['courier_type'] = 'customer'
        
        serializer4 = TransactionCreateSerializer(data=data4)
        if serializer4.is_valid():
            print("✅ Courier delivery with customer courier name validation passed")
        else:
            print(f"❌ Courier delivery with customer courier name validation failed: {serializer4.errors}")
        print()
        
        # Test Case 5: Courier delivery with system courier UUID
        print("📋 Test Case 5: Courier delivery with system courier UUID")
        system_courier = User.objects.filter(groups__name='Courier').first()
        if system_courier:
            data5 = data1.copy()
            data5['delivery_method'] = 'courier'
            data5['courier'] = system_courier.id
            
            serializer5 = TransactionCreateSerializer(data=data5)
            if serializer5.is_valid():
                print("✅ Courier delivery with system courier UUID validation passed")
            else:
                print(f"❌ Courier delivery with system courier UUID validation failed: {serializer5.errors}")
        else:
            print("⚠️  No system courier found, skipping test case 5")
        print()
        
        return True
        
    except Exception as e:
        print(f"❌ Test failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = test_courier_validation()
    if success:
        print("🎉 All courier validation tests completed!")
    else:
        print("💥 Courier validation tests failed!")
        sys.exit(1)
