{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Locations" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
.metric-card {
    border: none !important;
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075) !important;
}

.metric-card .card-title,
.metric-card h2,
.metric-card small,
.metric-card i {
    color: #000000 !important;
}

.metric-card[style*="#000d28"] .card-title,
.metric-card[style*="#000d28"] h2,
.metric-card[style*="#000d28"] small,
.metric-card[style*="#000d28"] i {
    color: #000000 !important;
}

.metric-card[style*="#013121"] .card-title,
.metric-card[style*="#013121"] h2,
.metric-card[style*="#013121"] small,
.metric-card[style*="#013121"] i {
    color: #000000 !important;
}

.metric-card[style*="#6a0000"] .card-title,
.metric-card[style*="#6a0000"] h2,
.metric-card[style*="#6a0000"] small,
.metric-card[style*="#6a0000"] i {
    color: #000000 !important;
}
</style>
{% endblock %}

{% block content %}
<div class="row">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h1 class="h2">
                <i class="bi bi-geo-alt"></i>
                {% trans "Locations" %}
            </h1>
            <div class="btn-group" role="group">
                <button class="btn btn-primary" id="add-location">
                    <i class="bi bi-plus-circle"></i>
                    {% trans "New Location" %}
                </button>
                <button class="btn btn-outline-secondary" id="location-stats">
                    <i class="bi bi-bar-chart"></i>
                    {% trans "Statistics" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Location Statistics Cards -->
<div class="row mb-4">
    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #000d28;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Total Locations" %}</h5>
                        <h2 class="mb-0 text-white" id="total-locations">-</h2>
                        <small class="opacity-75">{% trans "All locations" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-building fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #013121;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Active Locations" %}</h5>
                        <h2 class="mb-0 text-white" id="active-locations">-</h2>
                        <small class="opacity-75">{% trans "Currently operating" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-check-circle fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #6a0000;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Countries" %}</h5>
                        <h2 class="mb-0 text-white" id="total-countries">-</h2>
                        <small class="opacity-75">{% trans "Operating countries" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-globe fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-3">
        <div class="card text-white metric-card" style="background-color: #17a2b8;">
            <div class="card-body">
                <div class="d-flex justify-content-between">
                    <div>
                        <h5 class="card-title">{% trans "Total Users" %}</h5>
                        <h2 class="mb-0 text-white" id="total-users">-</h2>
                        <small class="opacity-75">{% trans "Across all locations" %}</small>
                    </div>
                    <div class="align-self-center">
                        <i class="bi bi-people fs-1"></i>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Filters -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title mb-0">
                    <i class="bi bi-funnel"></i>
                    {% trans "Filters" %}
                </h5>
            </div>
            <div class="card-body">
                <form id="filter-form" class="row g-3">
                    <div class="col-md-3">
                        <label for="status-filter" class="form-label">{% trans "Status" %}</label>
                        <select class="form-select" id="status-filter" name="status">
                            <option value="">{% trans "All Statuses" %}</option>
                            <option value="active">{% trans "Active" %}</option>
                            <option value="inactive">{% trans "Inactive" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="country-filter" class="form-label">{% trans "Country" %}</label>
                        <select class="form-select" id="country-filter" name="country">
                            <option value="">{% trans "All Countries" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="manager-filter" class="form-label">{% trans "Manager" %}</label>
                        <select class="form-select" id="manager-filter" name="manager">
                            <option value="">{% trans "All Managers" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-md-3">
                        <label for="main-office-filter" class="form-label">{% trans "Main Office" %}</label>
                        <select class="form-select" id="main-office-filter" name="is_main_office">
                            <option value="">{% trans "All Locations" %}</option>
                            <option value="true">{% trans "Main Office Only" %}</option>
                            <option value="false">{% trans "Branch Offices Only" %}</option>
                        </select>
                    </div>
                    
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="bi bi-search"></i>
                            {% trans "Apply Filters" %}
                        </button>
                        <button type="button" class="btn btn-outline-secondary" id="clear-filters">
                            <i class="bi bi-x-circle"></i>
                            {% trans "Clear Filters" %}
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Locations Table -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">
                    <i class="bi bi-list"></i>
                    {% trans "Location List" %}
                </h5>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refresh-btn">
                        <i class="bi bi-arrow-clockwise"></i>
                        {% trans "Refresh" %}
                    </button>
                    <button type="button" class="btn btn-outline-success btn-sm" id="export-btn">
                        <i class="bi bi-download"></i>
                        {% trans "Export" %}
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="locations-table">
                        <thead>
                            <tr>
                                <th>{% trans "Code" %}</th>
                                <th>{% trans "Name" %}</th>
                                <th>{% trans "Country/City" %}</th>
                                <th>{% trans "Manager" %}</th>
                                <th>{% trans "Status" %}</th>
                                <th>{% trans "Type" %}</th>
                                <th>{% trans "Commission Rate" %}</th>
                                <th>{% trans "Actions" %}</th>
                            </tr>
                        </thead>
                        <tbody>
                            <!-- Data will be populated by DataTables -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Location Modal -->
<div class="modal fade" id="locationModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="locationModalTitle">{% trans "Location Details" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="location-form">
                    <input type="hidden" id="location-id">
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-name" class="form-label">{% trans "Name" %} *</label>
                                <input type="text" class="form-control" id="location-name" name="name" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-code" class="form-label">{% trans "Code" %} *</label>
                                <input type="text" class="form-control" id="location-code" name="code" required maxlength="10" style="text-transform: uppercase;">
                                <div class="form-text">{% trans "2-10 uppercase letters" %}</div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-country" class="form-label">{% trans "Country" %} *</label>
                                <input type="text" class="form-control" id="location-country" name="country" required>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-city" class="form-label">{% trans "City" %} *</label>
                                <input type="text" class="form-control" id="location-city" name="city" required>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location-address" class="form-label">{% trans "Address" %}</label>
                        <textarea class="form-control" id="location-address" name="address" rows="3"></textarea>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-phone" class="form-label">{% trans "Phone Number" %}</label>
                                <input type="tel" class="form-control" id="location-phone" name="phone_number">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-email" class="form-label">{% trans "Email" %}</label>
                                <input type="email" class="form-control" id="location-email" name="email">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-timezone" class="form-label">{% trans "Timezone" %}</label>
                                <select class="form-select" id="location-timezone" name="timezone">
                                    <option value="Asia/Tehran">Asia/Tehran</option>
                                    <option value="Europe/Istanbul">Europe/Istanbul</option>
                                    <option value="Asia/Dubai">Asia/Dubai</option>
                                    <option value="Asia/Shanghai">Asia/Shanghai</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="location-manager" class="form-label">{% trans "Manager" %}</label>
                                <select class="form-select" id="location-manager" name="manager">
                                    <option value="">{% trans "Select Manager" %}</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="commission-rate" class="form-label">{% trans "Commission Rate (%)" %}</label>
                                <input type="number" class="form-control" id="commission-rate" name="commission_rate" step="0.0001" min="0" max="100">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="min-transaction" class="form-label">{% trans "Min Transaction" %}</label>
                                <input type="number" class="form-control" id="min-transaction" name="min_transaction_amount" step="0.01" min="0">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="mb-3">
                                <label for="max-transaction" class="form-label">{% trans "Max Transaction" %}</label>
                                <input type="number" class="form-control" id="max-transaction" name="max_transaction_amount" step="0.01" min="0">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="opening-time" class="form-label">{% trans "Opening Time" %}</label>
                                <input type="time" class="form-control" id="opening-time" name="opening_time">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="closing-time" class="form-label">{% trans "Closing Time" %}</label>
                                <input type="time" class="form-control" id="closing-time" name="closing_time">
                            </div>
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is-active" name="is_active" checked>
                                <label class="form-check-label" for="is-active">
                                    {% trans "Active" %}
                                </label>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-check mb-3">
                                <input class="form-check-input" type="checkbox" id="is-main-office" name="is_main_office">
                                <label class="form-check-label" for="is-main-office">
                                    {% trans "Main Office" %}
                                </label>
                            </div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="location-notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="location-notes" name="notes" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-primary" id="save-location">{% trans "Save Location" %}</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let locationsTable;
let isEditMode = false;

$(document).ready(function() {
    // Initialize page
    loadLocationStats();
    loadManagers();
    loadCountries();
    initializeLocationsTable();
    
    // Event handlers
    $('#filter-form').on('submit', function(e) {
        e.preventDefault();
        locationsTable.ajax.reload();
    });
    
    $('#clear-filters').on('click', function() {
        $('#filter-form')[0].reset();
        locationsTable.ajax.reload();
    });
    
    $('#refresh-btn').on('click', function() {
        locationsTable.ajax.reload();
        loadLocationStats();
    });
    
    $('#export-btn').on('click', function() {
        exportLocations();
    });
    
    $('#add-location').on('click', function() {
        showLocationModal();
    });
    
    $('#save-location').on('click', function() {
        saveLocation();
    });
    
    // Auto-uppercase location code
    $('#location-code').on('input', function() {
        $(this).val($(this).val().toUpperCase());
    });
});

function loadLocationStats() {
    $.ajax({
        url: '/api/v1/locations/locations/stats/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            $('#total-locations').text(data.total_locations || 0);
            $('#active-locations').text(data.active_locations || 0);
            $('#total-countries').text(data.locations_by_country ? Object.keys(data.locations_by_country).length : 0);
            $('#total-users').text(data.total_users || 0);
        },
        error: function(xhr) {
            console.error('Error loading location stats:', xhr);
        }
    });
}

function loadManagers() {
    $.ajax({
        url: '/api/v1/accounts/users/?role=admin,accountant,branch_employee',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const select = $('#location-manager');
            select.find('option:not(:first)').remove();
            
            if (data.results) {
                data.results.forEach(function(user) {
                    select.append(`<option value="${user.id}">${user.display_name} (${user.username})</option>`);
                });
            }
        }
    });
}

function loadCountries() {
    $.ajax({
        url: '/api/v1/locations/locations/',
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            const countries = [...new Set(data.results.map(location => location.country))];
            const select = $('#country-filter');
            select.find('option:not(:first)').remove();
            
            countries.forEach(function(country) {
                select.append(`<option value="${country}">${country}</option>`);
            });
        }
    });
}

function initializeLocationsTable() {
    // Check if DataTable already exists and destroy it
    if ($.fn.DataTable.isDataTable('#locations-table')) {
        $('#locations-table').DataTable().destroy();
        console.log('Existing locations DataTable destroyed');
    }

    const columns = [
        { 
            data: 'code', 
            name: 'code',
            render: function(data, type, row) {
                return `<code class="fw-bold">${data}</code>`;
            }
        },
        { 
            data: 'name', 
            name: 'name',
            render: function(data, type, row) {
                const mainOfficeIcon = row.is_main_office ? '<i class="bi bi-star-fill text-warning ms-1" title="Main Office"></i>' : '';
                return `${data}${mainOfficeIcon}`;
            }
        },
        { 
            data: null,
            render: function(data, type, row) {
                return `${row.country}, ${row.city}`;
            }
        },
        {
            data: 'manager_name',
            name: 'manager__first_name',
            defaultContent: '<span class="text-muted">{% trans "No manager assigned" %}</span>',
            render: function(data, type, row) {
                if (data && data !== null && data !== '') {
                    return data;
                }
                return '<span class="text-muted">{% trans "No manager assigned" %}</span>';
            }
        },
        { 
            data: 'is_active', 
            name: 'is_active',
            render: function(data, type, row) {
                const badgeClass = data ? 'bg-success' : 'bg-secondary';
                const text = data ? '{% trans "Active" %}' : '{% trans "Inactive" %}';
                return `<span class="badge ${badgeClass}">${text}</span>`;
            }
        },
        {
            data: 'is_main_office',
            render: function(data, type, row) {
                return data ? '<span class="badge bg-warning">{% trans "Main Office" %}</span>' : '<span class="badge bg-primary">{% trans "Branch" %}</span>';
            }
        },
        {
            data: 'commission_rate',
            render: function(data, type, row) {
                return data ? `${(parseFloat(data) * 100).toFixed(2)}%` : '-';
            }
        },
        {
            data: null,
            orderable: false,
            render: function(data, type, row) {
                return `
                    <div class="btn-group" role="group">
                        <button class="btn btn-sm btn-outline-primary" onclick="viewLocation('${row.id}')" title="{% trans 'View Details' %}">
                            <i class="bi bi-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-secondary" onclick="editLocation('${row.id}')" title="{% trans 'Edit' %}">
                            <i class="bi bi-pencil"></i>
                        </button>
                        <button class="btn btn-sm btn-outline-info" onclick="viewBalance('${row.id}')" title="{% trans 'View Balance' %}">
                            <i class="bi bi-wallet"></i>
                        </button>
                        ${row.is_active ? 
                            `<button class="btn btn-sm btn-outline-warning" onclick="deactivateLocation('${row.id}')" title="{% trans 'Deactivate' %}">
                                <i class="bi bi-pause"></i>
                            </button>` :
                            `<button class="btn btn-sm btn-outline-success" onclick="activateLocation('${row.id}')" title="{% trans 'Activate' %}">
                                <i class="bi bi-play"></i>
                            </button>`
                        }
                    </div>
                `;
            }
        }
    ];

    locationsTable = ArenaDoviz.tables.initServerSideTable(
        '#locations-table',
        '/api/v1/locations/locations/',
        columns,
        {
            order: [[0, 'asc']], // Order by code
            ajax: {
                url: '/api/v1/locations/locations/',
                type: 'GET',
                headers: getAuthHeaders(),
                data: function(d) {
                    // Add filter parameters
                    const formData = new FormData($('#filter-form')[0]);
                    const filters = {};
                    
                    for (let [key, value] of formData.entries()) {
                        if (value) {
                            filters[key] = value;
                        }
                    }
                    
                    return Object.assign({
                        page: Math.floor(d.start / d.length) + 1,
                        page_size: d.length,
                        search: d.search.value,
                        ordering: d.order.length > 0 ? 
                            (d.order[0].dir === 'desc' ? '-' : '') + columns[d.order[0].column].name : 
                            'code'
                    }, filters);
                },
                dataSrc: function(json) {
                    json.recordsTotal = json.count;
                    json.recordsFiltered = json.count;
                    return json.results;
                }
            }
        }
    );
}

function showLocationModal(locationData = null) {
    isEditMode = !!locationData;

    $('#locationModalTitle').text(isEditMode ? '{% trans "Edit Location" %}' : '{% trans "Add New Location" %}');
    $('#save-location').text(isEditMode ? '{% trans "Update Location" %}' : '{% trans "Save Location" %}');

    // Reset form
    $('#location-form')[0].reset();
    $('#location-id').val('');

    if (locationData) {
        // Populate form with location data
        $('#location-id').val(locationData.id);
        $('#location-name').val(locationData.name);
        $('#location-code').val(locationData.code);
        $('#location-country').val(locationData.country);
        $('#location-city').val(locationData.city);
        $('#location-address').val(locationData.address);
        $('#location-phone').val(locationData.phone_number);
        $('#location-email').val(locationData.email);
        $('#location-timezone').val(locationData.timezone);
        $('#location-manager').val(locationData.manager);
        $('#commission-rate').val(locationData.commission_rate);
        $('#min-transaction').val(locationData.min_transaction_amount);
        $('#max-transaction').val(locationData.max_transaction_amount);
        $('#opening-time').val(locationData.opening_time);
        $('#closing-time').val(locationData.closing_time);
        $('#is-active').prop('checked', locationData.is_active);
        $('#is-main-office').prop('checked', locationData.is_main_office);
        $('#location-notes').val(locationData.notes);
    }

    $('#locationModal').modal('show');
}

function saveLocation() {
    const formData = new FormData($('#location-form')[0]);
    const locationData = {};
    
    for (let [key, value] of formData.entries()) {
        if (key === 'is_active' || key === 'is_main_office') {
            locationData[key] = value === 'on';
        } else if (value) {
            locationData[key] = value;
        }
    }
    
    const locationId = $('#location-id').val();
    const url = locationId ? `/api/v1/locations/locations/${locationId}/` : '/api/v1/locations/locations/';
    const method = locationId ? 'PUT' : 'POST';
    
    $.ajax({
        url: url,
        method: method,
        headers: getAuthHeaders(),
        data: JSON.stringify(locationData),
        success: function(data) {
            $('#locationModal').modal('hide');
            locationsTable.ajax.reload();
            loadLocationStats();
            showAlert('success', isEditMode ? '{% trans "Location updated successfully!" %}' : '{% trans "Location created successfully!" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.error || (isEditMode ? '{% trans "Failed to update location" %}' : '{% trans "Failed to create location" %}');
            showAlert('danger', error);
        }
    });
}

function viewLocation(locationId) {
    window.location.href = `/locations/${locationId}/`;
}

function editLocation(locationId) {
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/`,
        method: 'GET',
        headers: getAuthHeaders(),
        success: function(data) {
            showLocationModal(data);
        },
        error: function(xhr) {
            showAlert('danger', '{% trans "Failed to load location details" %}');
        }
    });
}

function viewBalance(locationId) {
    window.location.href = `/locations/${locationId}/balance/`;
}

function deactivateLocation(locationId) {
    if (confirm('{% trans "Are you sure you want to deactivate this location?" %}')) {
        $.ajax({
            url: `/api/v1/locations/locations/${locationId}/deactivate/`,
            method: 'POST',
            headers: getAuthHeaders(),
            success: function(data) {
                locationsTable.ajax.reload();
                loadLocationStats();
                showAlert('success', '{% trans "Location deactivated successfully!" %}');
            },
            error: function(xhr) {
                const error = xhr.responseJSON?.message || '{% trans "Failed to deactivate location" %}';
                showAlert('danger', error);
            }
        });
    }
}

function activateLocation(locationId) {
    $.ajax({
        url: `/api/v1/locations/locations/${locationId}/activate/`,
        method: 'POST',
        headers: getAuthHeaders(),
        success: function(data) {
            locationsTable.ajax.reload();
            loadLocationStats();
            showAlert('success', '{% trans "Location activated successfully!" %}');
        },
        error: function(xhr) {
            const error = xhr.responseJSON?.message || '{% trans "Failed to activate location" %}';
            showAlert('danger', error);
        }
    });
}

function exportLocations() {
    const formData = new FormData($('#filter-form')[0]);
    const params = new URLSearchParams();
    
    for (let [key, value] of formData.entries()) {
        if (value) {
            params.append(key, value);
        }
    }
    
    params.append('format', 'excel');
    
    window.open('/api/v1/locations/locations/export/?' + params.toString());
}

function getAuthHeaders() {
    const token = ArenaDoviz.auth.getAccessToken() || '';
    const headers = {
        'Content-Type': 'application/json'
    };

    if (token) {
        headers['Authorization'] = 'Bearer ' + token;
    }

    const csrfToken = $('[name=csrfmiddlewaretoken]').val() ||
                     $('meta[name=csrf-token]').attr('content') ||
                     document.querySelector('[name=csrfmiddlewaretoken]')?.value;
    if (csrfToken) {
        headers['X-CSRFToken'] = csrfToken;
    }

    return headers;
}

function showAlert(type, message) {
    const alert = `
        <div class="alert alert-${type} alert-dismissible fade show" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('main .container-fluid').prepend(alert);
    
    setTimeout(function() {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
