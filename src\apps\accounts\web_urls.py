"""
Web URL configuration for Arena Doviz Accounts app.
"""

from django.urls import path
from django.contrib.auth import views as auth_views
from django.views.generic import TemplateView
from . import web_views

app_name = 'accounts_web'

urlpatterns = [
    # Authentication URLs
    path('login/', auth_views.LoginView.as_view(
        template_name='accounts/simple_login.html',
        redirect_authenticated_user=True
    ), name='login'),
    path('logout/', auth_views.LogoutView.as_view(), name='logout'),
    path('profile/', TemplateView.as_view(template_name='accounts/profile.html'), name='profile'),

    # User Management URLs
    path('users/', web_views.user_management, name='user_management'),
    path('users/<uuid:user_id>/', web_views.user_detail, name='user_detail'),

    # AJAX endpoints for user management
    path('api/user-stats/', web_views.get_user_stats, name='api_user_stats'),
    path('api/user-list/', web_views.get_user_list, name='api_user_list'),
    path('api/locations/', web_views.get_locations_for_select, name='api_locations'),
    path('api/users/<uuid:user_id>/unlock/', web_views.unlock_user_account, name='api_unlock_user'),
]
