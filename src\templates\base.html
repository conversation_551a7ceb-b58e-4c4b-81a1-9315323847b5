{% load static %}
{% load i18n %}
{% get_current_language as LANGUAGE_CODE %}
<!DOCTYPE html>
<html lang="{{ LANGUAGE_CODE }}" dir="{% if LANGUAGE_CODE == 'fa' %}rtl{% else %}ltr{% endif %}">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <meta name="csrf-token" content="{{ csrf_token }}">
    <title>{% block title %}Arena Doviz - Exchange Accounting System{% endblock %}</title>

    <!-- Bootstrap 5 CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Bootstrap Icons -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.10.0/font/bootstrap-icons.css" rel="stylesheet">

    <!-- Custom CSS -->
    <link href="{% static 'css/arena-doviz.css' %}" rel="stylesheet">

    <!-- Arena Doviz Design System -->
    <link href="{% static 'css/arena-doviz-design.css' %}" rel="stylesheet">

    {% if LANGUAGE_CODE == 'fa' %}
    <!-- RTL Support for Persian -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <link href="{% static 'css/rtl.css' %}" rel="stylesheet">
    {% endif %}
    
    {% block extra_css %}{% endblock %}
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'core_web:dashboard' %}">
                <i class="bi bi-currency-exchange"></i>
                Arena Doviz
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'core_web:dashboard' %}">
                            <i class="bi bi-speedometer2"></i>
                            {% trans "Dashboard" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'core_web:analytics' %}">
                            <i class="bi bi-graph-up"></i>
                            {% trans "Analytics" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'customers_web:list' %}">
                            <i class="bi bi-people"></i>
                            {% trans "Customers" %}
                        </a>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="transactionsDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-arrow-left-right"></i>
                            {% trans "Transactions" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="transactionsDropdown">
                            <li><a class="dropdown-item" href="{% url 'transactions_web:navigation' %}">
                                <i class="bi bi-grid-3x3-gap"></i>
                                {% trans "Transaction Center" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'transactions_web:list' %}">
                                <i class="bi bi-list"></i>
                                {% trans "All Transactions" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'transactions_web:type_list' 'EXCHANGE' %}">
                                <i class="bi bi-arrow-left-right"></i>
                                {% trans "Currency Exchange" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'transactions_web:type_list' 'TRANSFER' %}">
                                <i class="bi bi-send"></i>
                                {% trans "Money Transfer" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'transactions_web:type_list' 'DEPOSIT' %}">
                                <i class="bi bi-cash-coin"></i>
                                {% trans "Cash Deposit" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'transactions_web:type_list' 'WITHDRAWAL' %}">
                                <i class="bi bi-cash-stack"></i>
                                {% trans "Cash Withdrawal" %}
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <i class="bi bi-gear"></i>
                            {% trans "Administration" %}
                        </a>
                        <ul class="dropdown-menu" aria-labelledby="adminDropdown">
                            <li><a class="dropdown-item" href="{% url 'users_web:user_management' %}">
                                <i class="bi bi-people"></i>
                                {% trans "User Management" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'locations_web:list' %}">
                                <i class="bi bi-geo-alt"></i>
                                {% trans "Location Management" %}
                            </a></li>
                            <li><a class="dropdown-item" href="{% url 'currencies_web:list' %}">
                                <i class="bi bi-currency-exchange"></i>
                                {% trans "Currency Management" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="{% url 'currencies_web:rate_management' %}">
                                <i class="bi bi-arrow-left-right"></i>
                                {% trans "Exchange Rate Management" %}
                            </a></li>
                        </ul>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'reports_web:list' %}">
                            <i class="bi bi-file-earmark-text"></i>
                            {% trans "Reports" %}
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="navbarDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="bi bi-person-circle"></i>
                            {{ user.get_display_name }}
                        </a>
                        <ul class="dropdown-menu">
                            <li><a class="dropdown-item" href="{% url 'accounts_web:profile' %}">
                                <i class="bi bi-person"></i> {% trans "Profile" %}
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" onclick="handleLogout()">
                                <i class="bi bi-box-arrow-right"></i> {% trans "Logout" %}
                            </a></li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'accounts_web:login' %}">
                            <i class="bi bi-box-arrow-in-right"></i>
                            {% trans "Login" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
    
    <!-- Main Content -->
    <main class="container-fluid py-4">
        {% if messages %}
        <div class="row">
            <div class="col-12">
                {% for message in messages %}
                <div class="alert alert-{{ message.tags }} alert-dismissible fade show" role="alert">
                    {{ message }}
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
                {% endfor %}
            </div>
        </div>
        {% endif %}
        
        {% block content %}{% endblock %}
    </main>
    
    <!-- Footer -->
    <footer class="bg-light py-3 mt-5">
        <div class="container-fluid">
            <div class="row">
                <div class="col-md-6">
                    <p class="mb-0 text-muted">
                        &copy; {% now "Y" %} Arena Doviz Exchange Accounting System
                    </p>
                </div>
                <div class="col-md-6 text-end">
                    <p class="mb-0 text-muted">
                        {% trans "Version" %} 1.0.0
                    </p>
                </div>
            </div>
        </div>
    </footer>
    
    <!-- Bootstrap 5 JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- jQuery -->
    <script src="{% static 'js/error-monitor.js' %}"></script>
    <script src="https://code.jquery.com/jquery-3.7.0.min.js"></script>
    
    <!-- Chart.js -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>

    <!-- DataTables -->
    <link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.13.6/css/dataTables.bootstrap5.min.css">
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/jquery.dataTables.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/1.13.6/js/dataTables.bootstrap5.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/dataTables.responsive.min.js"></script>
    <script type="text/javascript" src="https://cdn.datatables.net/responsive/2.5.0/js/responsive.bootstrap5.min.js"></script>
    
    <!-- Custom JS -->
    <script src="{% static 'js/arena-doviz.js' %}?v={{ request.user.id|default:'1' }}{{ request.user.last_login.timestamp|default:'1' }}"></script>
    <script src="{% static 'js/charts.js' %}?v={{ request.user.id|default:'1' }}{{ request.user.last_login.timestamp|default:'1' }}"></script>

    <script>
    // Global JWT authentication handling
    function handleLogout() {
        if (confirm('{% trans "Are you sure you want to logout?" %}')) {
            ArenaDoviz.auth.logout();
        }
    }

    // Global error handler for JavaScript errors
    window.addEventListener('error', function(event) {
        // Filter out common third-party errors that we can't control
        const ignoredErrors = [
            'MutationObserver',
            'Cross-Origin-Opener-Policy',
            'Script error',
            'Non-Error promise rejection captured',
            'runtime.lastError',
            'message channel closed',
            'Extension context invalidated'
        ];

        const shouldIgnore = ignoredErrors.some(ignored =>
            event.message && event.message.includes(ignored)
        );

        if (!shouldIgnore) {
            console.error('JavaScript Error:', {
                message: event.message,
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                error: event.error
            });
        }

        // Don't show alerts for every error, just log them
        // You can uncomment the line below if you want to see error alerts
        // showAlert('error', 'A JavaScript error occurred. Please check the console for details.');
    });

    // Global unhandled promise rejection handler
    window.addEventListener('unhandledrejection', function(event) {
        // Filter out browser extension errors
        const errorMessage = event.reason ? event.reason.toString() : '';
        const ignoredPromiseErrors = [
            'runtime.lastError',
            'message channel closed',
            'Extension context invalidated',
            'chrome-extension://',
            'moz-extension://'
        ];

        const shouldIgnore = ignoredPromiseErrors.some(ignored =>
            errorMessage.includes(ignored)
        );

        if (!shouldIgnore) {
            console.error('Unhandled Promise Rejection:', event.reason);
        }

        // Prevent the default browser behavior
        event.preventDefault();
    });

    // Set session authentication status for JavaScript
    window.isSessionAuthenticated = {% if user.is_authenticated %}true{% else %}false{% endif %};

    // Check authentication status on page load
    $(document).ready(function() {
        // Skip authentication check on login/logout pages
        const currentPath = window.location.pathname;
        if (currentPath.includes('/accounts/login/') || currentPath.includes('/accounts/logout/')) {
            // Clear logout flag on login/logout pages
            localStorage.removeItem('arena_logout_in_progress');
            return;
        }

        // Check if logout is in progress - if so, don't auto-login
        const logoutInProgress = localStorage.getItem('arena_logout_in_progress');
        if (logoutInProgress) {
            console.log('Logout in progress, skipping auto-authentication');
            // Clear the flag after a short delay to allow logout to complete
            setTimeout(() => {
                localStorage.removeItem('arena_logout_in_progress');
            }, 2000);
            return;
        }

        // Check if user is authenticated via Django session but missing JWT tokens
        {% if user.is_authenticated %}
        if (!ArenaDoviz.auth.isAuthenticated()) {
            console.log('User is session-authenticated but missing JWT tokens. Getting JWT tokens...');
            getJWTTokensForSessionUser();
        } else {
            // Update user display in navbar if authenticated via JWT
            const userData = localStorage.getItem('arena_user_data');
            if (userData) {
                const user = JSON.parse(userData);
                updateUserDisplay(user);
            }
        }
        {% else %}
        // User is not session-authenticated, clear any stale JWT tokens
        if (ArenaDoviz.auth.isAuthenticated()) {
            console.log('Clearing stale JWT tokens for non-authenticated user');
            ArenaDoviz.auth.clearTokens();
        }
        {% endif %}
    });

    function updateUserDisplay(user) {
        // Update the user display name in navbar
        const displayName = user.first_name && user.last_name
            ? `${user.first_name} ${user.last_name}`
            : user.username;

        // Find and update the user display element
        const userDisplayElement = document.querySelector('#navbarDropdown');
        if (userDisplayElement) {
            userDisplayElement.innerHTML = `
                <i class="bi bi-person-circle"></i>
                ${displayName}
            `;
        }
    }

    function showAlert(type, message) {
        // Create alert element
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;

        // Insert at the top of main content
        const main = document.querySelector('main');
        if (main) {
            main.insertBefore(alertDiv, main.firstChild);
        }

        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }

    function getJWTTokensForSessionUser() {
        // Get JWT tokens for a user who is already authenticated via Django session
        // This is needed when users log in via the Django form instead of the JWT endpoint

        {% if user.is_authenticated %}
        // Use the user's credentials to get JWT tokens
        // Since we don't have the password, we'll use a special endpoint for session users
        fetch('/api/v1/accounts/users/get_jwt_tokens/', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRFToken': ArenaDoviz.utils.getCSRFToken()
            },
            credentials: 'include' // Include session cookies
        })
        .then(response => {
            if (!response.ok) {
                throw new Error('Failed to get JWT tokens');
            }
            return response.json();
        })
        .then(data => {
            console.log('JWT tokens obtained for session user');
            // Store tokens and user data
            ArenaDoviz.auth.setTokens(data.access, data.refresh);
            localStorage.setItem('arena_user_data', JSON.stringify(data.user));

            // Update user display
            updateUserDisplay(data.user);

            // Reload the page to ensure all components work with JWT tokens
            setTimeout(() => {
                window.location.reload();
            }, 500);
        })
        .catch(error => {
            console.error('Failed to get JWT tokens for session user:', error);
            // Continue without JWT tokens - user can still use session authentication
        });
        {% endif %}
    }

    // Global AJAX error handler for token refresh
    $(document).ajaxError(function(event, xhr, settings) {
        if (xhr.status === 401 && xhr.responseJSON && xhr.responseJSON.code === 'token_not_valid') {
            console.log('Token expired, attempting refresh...');

            // Try to refresh the token
            ArenaDoviz.auth.refreshToken()
                .then(newToken => {
                    console.log('Token refreshed successfully, retrying request...');

                    // Update the authorization header with new token
                    if (settings.headers) {
                        settings.headers['Authorization'] = 'Bearer ' + newToken;
                    }

                    // Retry the original request
                    $.ajax(settings);
                })
                .catch(error => {
                    console.error('Token refresh failed:', error);
                    // Clear JWT tokens and redirect to login if needed
                    ArenaDoviz.auth.clearTokens();

                    // Only redirect if not already on login page
                    if (!window.location.pathname.includes('/accounts/login/')) {
                        showAlert('warning', 'Your session has expired. Please log in again.');
                        setTimeout(() => {
                            window.location.href = '/accounts/login/';
                        }, 2000);
                    }
                });
        }
    });

    // Set up automatic token refresh (only if JWT authenticated)
    setInterval(function() {
        if (ArenaDoviz.auth.isAuthenticated()) {
            ArenaDoviz.auth.refreshToken().catch(error => {
                console.error('Token refresh failed:', error);
                // Clear JWT tokens but don't redirect
                // User might still be authenticated via Django session
                ArenaDoviz.auth.clearTokens();
            });
        }
    }, 50 * 60 * 1000); // Refresh every 50 minutes

    // Global authentication headers function
    window.getAuthHeaders = function() {
        return ArenaDoviz.auth.getAuthHeaders();
    };
    </script>

    {% block extra_js %}{% endblock %}
</body>
</html>
