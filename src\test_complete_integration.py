#!/usr/bin/env python
"""
Comprehensive test script to verify frontend-backend integration for all transaction types.
This script tests the exact data structures that each frontend form sends.
"""

import os
import sys
import django
from decimal import Decimal

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'arena_doviz.settings')
django.setup()

from apps.transactions.serializers import TransactionCreateSerializer
from apps.transactions.models import TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency

def get_test_data():
    """Get common test data for all transaction types."""
    transaction_types = {
        'DEPOSIT': TransactionType.objects.filter(code='DEPOSIT').first(),
        'WITHDRAWAL': TransactionType.objects.filter(code='WITHDRAWAL').first(),
        'EXCHANGE': TransactionType.objects.filter(code='EXCHANGE').first(),
    }
    
    customer = Customer.objects.first()
    location = Location.objects.first()
    usd = Currency.objects.filter(code='USD').first()
    aed = Currency.objects.filter(code='AED').first()
    
    return transaction_types, customer, location, usd, aed

def test_deposit_transaction():
    """Test deposit transaction with courier field (simulating original error)."""
    print("🧪 Testing Deposit Transaction")
    print("-" * 40)
    
    transaction_types, customer, location, usd, aed = get_test_data()
    
    # Simulate the exact data that caused the original error
    data = {
        'csrfmiddlewaretoken': 'test_token',
        'transaction_type_code': 'DEPOSIT',
        'customer': str(customer.id),
        'location': str(location.id),
        'reference_number': '',
        'description': '',
        'from_currency': str(usd.id),
        'from_amount': 10,
        'commission_amount': 0.005,
        'deposit_source': 'cash',
        'delivery_method': 'cash',  # Frontend hardcodes this
        'courier': 'test',  # This caused the original error
        'bank_reference': '',
        'check_number': '',
        'received_by': 'Current User',
        'receipt_number': 'RCP-*************',
        'cash_verified': True,
        'document_files': {},
        'document_type': 'other',
        'notes': '',
        'transaction_type': str(transaction_types['DEPOSIT'].id),
        'to_amount': 10,
        'exchange_rate': 1,
        'status': 'pending',
        'to_currency': str(usd.id)
    }
    
    serializer = TransactionCreateSerializer(data=data)
    if serializer.is_valid():
        print("✅ Deposit transaction validation PASSED")
        print(f"   - Courier field handled correctly")
        print(f"   - All frontend fields accepted")
        return True
    else:
        print("❌ Deposit transaction validation FAILED")
        print(f"   - Errors: {serializer.errors}")
        return False

def test_withdrawal_transaction():
    """Test withdrawal transaction with courier selection."""
    print("\n🧪 Testing Withdrawal Transaction")
    print("-" * 40)
    
    transaction_types, customer, location, usd, aed = get_test_data()
    
    data = {
        'transaction_type_code': 'WITHDRAWAL',
        'customer': str(customer.id),
        'location': str(location.id),
        'from_currency': str(usd.id),
        'from_amount': 50,
        'commission_amount': 1.0,
        'withdrawal_method': 'cash',
        'delivery_method': 'courier',  # User selected courier
        'courier': 'John Doe',  # Customer courier name
        'authorized_by': 'Manager',
        'customer_id_verified': True,
        'signature_verified': True,
        'cash_dispensed': False,
        'to_currency': str(usd.id),
        'to_amount': 50,
        'exchange_rate': 1,
        'status': 'pending'
    }
    
    serializer = TransactionCreateSerializer(data=data)
    if serializer.is_valid():
        print("✅ Withdrawal transaction validation PASSED")
        print(f"   - Customer courier name handled correctly")
        print(f"   - Delivery method respected")
        return True
    else:
        print("❌ Withdrawal transaction validation FAILED")
        print(f"   - Errors: {serializer.errors}")
        return False

def test_exchange_transaction():
    """Test currency exchange transaction."""
    print("\n🧪 Testing Exchange Transaction")
    print("-" * 40)
    
    transaction_types, customer, location, usd, aed = get_test_data()
    
    data = {
        'transaction_type_code': 'EXCHANGE',
        'customer': str(customer.id),
        'location': str(location.id),
        'from_currency': str(usd.id),
        'to_currency': str(aed.id),
        'from_amount': 100,
        'to_amount': 367.5,  # Example exchange rate
        'exchange_rate': 3.675,
        'commission_amount': 2.0,
        'delivery_method': 'in_person',
        'status': 'pending'
    }
    
    serializer = TransactionCreateSerializer(data=data)
    if serializer.is_valid():
        print("✅ Exchange transaction validation PASSED")
        print(f"   - Currency exchange handled correctly")
        print(f"   - Exchange rate validation passed")
        return True
    else:
        print("❌ Exchange transaction validation FAILED")
        print(f"   - Errors: {serializer.errors}")
        return False

def test_internal_transfer():
    """Test internal transfer transaction."""
    print("\n🧪 Testing Internal Transfer Transaction")
    print("-" * 40)
    
    transaction_types, customer, location, usd, aed = get_test_data()
    recipient = Customer.objects.exclude(id=customer.id).first()
    
    if not recipient:
        print("⚠️  No recipient customer found, skipping internal transfer test")
        return True
    
    data = {
        'transaction_type_code': 'INTERNAL_TRANSFER',
        'customer': str(customer.id),
        'recipient_customer': str(recipient.id),
        'location': str(location.id),
        'from_currency': str(usd.id),
        'from_amount': 200,
        'commission_amount': 5.0,
        'internal_reference': 'INT-REF-123',
        'delivery_method': 'internal',
        'to_currency': str(usd.id),
        'to_amount': 200,
        'exchange_rate': 1,
        'status': 'pending'
    }
    
    serializer = TransactionCreateSerializer(data=data)
    if serializer.is_valid():
        print("✅ Internal transfer validation PASSED")
        print(f"   - Recipient customer handled correctly")
        print(f"   - Internal reference mapped correctly")
        return True
    else:
        print("❌ Internal transfer validation FAILED")
        print(f"   - Errors: {serializer.errors}")
        return False

def run_all_tests():
    """Run all integration tests."""
    print("🚀 Starting Complete Frontend-Backend Integration Tests")
    print("=" * 60)
    
    try:
        results = []
        results.append(test_deposit_transaction())
        results.append(test_withdrawal_transaction())
        results.append(test_exchange_transaction())
        results.append(test_internal_transfer())
        
        print("\n" + "=" * 60)
        print("📊 Test Results Summary")
        print("=" * 60)
        
        passed = sum(results)
        total = len(results)
        
        print(f"✅ Passed: {passed}/{total}")
        print(f"❌ Failed: {total - passed}/{total}")
        
        if passed == total:
            print("\n🎉 ALL TESTS PASSED! Frontend-backend integration is working correctly.")
            return True
        else:
            print(f"\n💥 {total - passed} tests failed. Integration needs more work.")
            return False
            
    except Exception as e:
        print(f"❌ Test suite failed with exception: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    success = run_all_tests()
    sys.exit(0 if success else 1)
