"""
Web views for Arena Doviz Transactions app.
Handles HTML template rendering for transaction management interface.
"""

from django.shortcuts import render, get_object_or_404, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext as _
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from .models import Transaction, TransactionType
from apps.customers.models import Customer
from apps.locations.models import Location
from apps.currencies.models import Currency
import logging

logger = logging.getLogger(__name__)


@login_required
def transaction_navigation(request):
    """Display transaction navigation page with type-specific options."""
    context = {
        'page_title': _('Transaction Management'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': None}
        ]
    }
    return render(request, 'transactions/navigation.html', context)


@login_required
def transaction_test_forms(request):
    """Test page for transaction forms functionality."""
    context = {
        'page_title': _('Transaction Forms Test'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': reverse('transactions_web:navigation')},
            {'name': _('Test Forms'), 'url': None}
        ]
    }
    return render(request, 'transactions/test_forms.html', context)


@login_required
def transaction_list(request):
    """Display transaction list page."""
    context = {
        'page_title': _('All Transactions'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('All Transactions'), 'url': None}
        ]
    }
    return render(request, 'transactions/list.html', context)


@login_required
def transaction_add(request):
    """Display transaction add form."""
    context = {
        'page_title': _('New Transaction'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('New Transaction'), 'url': None}
        ]
    }
    return render(request, 'transactions/add.html', context)


@login_required
def transaction_detail(request, transaction_id):
    """Display transaction detail page."""
    transaction = get_object_or_404(
        Transaction.objects.select_related(
            'customer', 'location', 'transaction_type',
            'from_currency', 'to_currency', 'commission_currency',
            'created_by', 'updated_by', 'approved_by', 'courier'
        ).prefetch_related('balance_entries', 'child_transactions'),
        id=transaction_id,
        is_deleted=False
    )
    
    # Check if user can view this transaction
    if not request.user.can_manage_users() and request.user.location != transaction.location:
        messages.error(request, _('You do not have permission to view this transaction.'))
        return redirect('transactions_web:list')
    
    context = {
        'transaction': transaction,
        'page_title': f'{_("Transaction")} {transaction.transaction_number}',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': transaction.transaction_number, 'url': None}
        ]
    }
    return render(request, 'transactions/detail.html', context)


@login_required
def transaction_edit(request, transaction_id):
    """Display transaction edit form."""
    transaction = get_object_or_404(
        Transaction.objects.select_related(
            'customer', 'location', 'transaction_type',
            'from_currency', 'to_currency', 'commission_currency'
        ),
        id=transaction_id,
        is_deleted=False
    )
    
    # Check if user can edit this transaction
    if not request.user.can_manage_users() and request.user.location != transaction.location:
        messages.error(request, _('You do not have permission to edit this transaction.'))
        return redirect('transactions_web:detail', transaction_id=transaction_id)
    
    # Check if transaction can be edited
    if transaction.status not in [Transaction.Status.DRAFT, Transaction.Status.REJECTED]:
        messages.error(request, _('This transaction cannot be edited in its current state.'))
        return redirect('transactions_web:detail', transaction_id=transaction_id)
    
    context = {
        'transaction': transaction,
        'page_title': f'{_("Edit Transaction")} {transaction.transaction_number}',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': transaction.transaction_number, 'url': f'/transactions/{transaction_id}/'},
            {'name': _('Edit'), 'url': None}
        ]
    }
    return render(request, 'transactions/edit.html', context)


@login_required
def transaction_approve_page(request, transaction_id):
    """Display transaction approval page."""
    transaction = get_object_or_404(
        Transaction.objects.select_related(
            'customer', 'location', 'transaction_type',
            'from_currency', 'to_currency', 'commission_currency'
        ),
        id=transaction_id,
        is_deleted=False
    )
    
    # Check if user can approve transactions
    if not request.user.can_approve_transactions():
        messages.error(request, _('You do not have permission to approve transactions.'))
        return redirect('transactions_web:detail', transaction_id=transaction_id)
    
    # Check if transaction can be approved
    if not transaction.can_be_approved():
        messages.error(request, _('This transaction cannot be approved in its current state.'))
        return redirect('transactions_web:detail', transaction_id=transaction_id)
    
    context = {
        'transaction': transaction,
        'page_title': f'{_("Approve Transaction")} {transaction.transaction_number}',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': transaction.transaction_number, 'url': f'/transactions/{transaction_id}/'},
            {'name': _('Approve'), 'url': None}
        ]
    }
    return render(request, 'transactions/approve.html', context)


@login_required
def transaction_type_list(request, transaction_type_code):
    """Display transaction list page filtered by type."""
    try:
        transaction_type = TransactionType.objects.get(code=transaction_type_code, is_deleted=False)
    except TransactionType.DoesNotExist:
        messages.error(request, _('Transaction type not found.'))
        return redirect('transactions_web:navigation')

    # Define icon classes for each transaction type
    icon_classes = {
        'EXCHANGE': 'arrow-left-right',
        'TRANSFER': 'send',
        'DEPOSIT': 'cash-coin',
        'WITHDRAWAL': 'cash-stack',
        'REMITTANCE': 'globe',
        'ADJUSTMENT': 'calculator'
    }

    context = {
        'transaction_type': transaction_type,
        'page_title': transaction_type.name,
        'icon_class': icon_classes.get(transaction_type_code, 'arrow-left-right'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': transaction_type.name, 'url': None}
        ]
    }
    return render(request, 'transactions/type_list.html', context)


@login_required
def transaction_type_add(request, transaction_type_code):
    """Display transaction add form for specific type."""
    try:
        transaction_type = TransactionType.objects.get(code=transaction_type_code, is_deleted=False)
    except TransactionType.DoesNotExist:
        messages.error(request, _('Transaction type not found.'))
        return redirect('transactions_web:navigation')

    # Special handling for TRANSFER - redirect to transfer navigation
    if transaction_type_code == 'TRANSFER':
        messages.info(request, _('Please select the specific transfer type (Internal, External, or International).'))
        return redirect('transactions_web:transfer_navigation')

    # Define template mappings for each transaction type
    template_mappings = {
        'EXCHANGE': 'transactions/forms/exchange.html',
        'BUY': 'transactions/forms/exchange.html',  # Use exchange form for buy
        'SELL': 'transactions/forms/exchange.html',  # Use exchange form for sell
        'DEPOSIT': 'transactions/forms/deposit.html',
        'WITHDRAWAL': 'transactions/forms/withdrawal.html',
        'TRANSFER': 'transactions/forms/transfer.html',
        'EXTERNAL_TRANSFER': 'transactions/forms/external_transfer.html',
        'INTERNAL_TRANSFER': 'transactions/forms/internal_transfer.html',
        'INTERNATIONAL_TRANSFER': 'transactions/forms/international_transfer.html',
        'REMITTANCE': 'transactions/forms/remittance.html',
        'ADJUSTMENT': 'transactions/forms/adjustment.html'
    }

    # Define icon classes for each transaction type
    icon_classes = {
        'EXCHANGE': 'arrow-left-right',
        'BUY': 'arrow-down',
        'SELL': 'arrow-up',
        'TRANSFER': 'send',
        'EXTERNAL_TRANSFER': 'bank',
        'INTERNAL_TRANSFER': 'arrow-left-right',
        'INTERNATIONAL_TRANSFER': 'globe',
        'DEPOSIT': 'cash-coin',
        'WITHDRAWAL': 'cash-stack',
        'REMITTANCE': 'globe',
        'ADJUSTMENT': 'calculator'
    }

    template_name = template_mappings.get(transaction_type_code, 'transactions/add.html')

    context = {
        'transaction_type': transaction_type,
        'transaction_type_code': transaction_type_code,
        'page_title': f'{_("New")} {transaction_type.name}',
        'form_title': f'{transaction_type.name} {_("Details")}',
        'icon_class': icon_classes.get(transaction_type_code, 'plus-circle'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': transaction_type.name, 'url': f'/transactions/type/{transaction_type_code}/'},
            {'name': _('New'), 'url': None}
        ]
    }
    return render(request, template_name, context)


@login_required
def pending_approvals(request):
    """Display pending approvals page."""
    context = {
        'page_title': _('Pending Approvals'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Pending Approvals'), 'url': None}
        ]
    }
    return render(request, 'transactions/pending_approvals.html', context)


@login_required
def transaction_reports(request):
    """Display transaction reports page."""
    context = {
        'page_title': _('Transaction Reports'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Reports'), 'url': None}
        ]
    }
    return render(request, 'transactions/reports.html', context)


# Transfer Sub-Type Views
@login_required
def transfer_navigation(request):
    """Transfer navigation page with sub-type options"""
    context = {
        'page_title': _('Money Transfer'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': None}
        ]
    }
    return render(request, 'transactions/transfer_navigation.html', context)


@login_required
def cash_navigation(request):
    """Cash transaction navigation page with deposit and withdrawal options"""
    context = {
        'page_title': _('Cash Transactions'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Cash Transactions'), 'url': None}
        ]
    }
    return render(request, 'transactions/cash_navigation.html', context)


@login_required
def internal_transfer_list(request):
    """List internal transfers"""
    context = {
        'page_title': _('Internal Transfers'),
        'transfer_type': 'internal',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('Internal Transfers'), 'url': None}
        ]
    }
    return render(request, 'transactions/transfer_list.html', context)


@login_required
def internal_transfer_add(request):
    """Add new internal transfer"""
    # Get the TRANSFER transaction type for context
    try:
        transaction_type = TransactionType.objects.get(code='TRANSFER', is_deleted=False)
    except TransactionType.DoesNotExist:
        transaction_type = None

    # Get customer parameter from URL if present
    customer_id = request.GET.get('customer')

    context = {
        'page_title': _('New Internal Transfer'),
        'transfer_type': 'internal',
        'transaction_type': transaction_type,
        'transaction_type_code': 'TRANSFER',
        'form_title': _('Internal Transfer Details'),
        'icon_class': 'arrow-left-right',
        'back_url': '/transactions/transfer/internal/',
        'customer_id': customer_id,  # Pass customer ID to template
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('Internal Transfers'), 'url': '/transactions/transfer/internal/'},
            {'name': _('Add New'), 'url': None}
        ]
    }
    return render(request, 'transactions/forms/internal_transfer.html', context)


@login_required
def external_transfer_list(request):
    """List external transfers"""
    context = {
        'page_title': _('External Transfers'),
        'transfer_type': 'external',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('External Transfers'), 'url': None}
        ]
    }
    return render(request, 'transactions/transfer_list.html', context)


@login_required
def external_transfer_add(request):
    """Add new external transfer"""
    # Get the TRANSFER transaction type for context
    try:
        transaction_type = TransactionType.objects.get(code='TRANSFER', is_deleted=False)
    except TransactionType.DoesNotExist:
        transaction_type = None

    # Get customer parameter from URL if present
    customer_id = request.GET.get('customer')

    context = {
        'page_title': _('New External Transfer'),
        'transfer_type': 'external',
        'transaction_type': transaction_type,
        'transaction_type_code': 'TRANSFER',
        'form_title': _('External Transfer Details'),
        'icon_class': 'bank',
        'back_url': '/transactions/transfer/external/',
        'customer_id': customer_id,  # Pass customer ID to template
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('External Transfers'), 'url': '/transactions/transfer/external/'},
            {'name': _('Add New'), 'url': None}
        ]
    }
    return render(request, 'transactions/forms/external_transfer.html', context)


@login_required
def international_transfer_list(request):
    """List international transfers"""
    context = {
        'page_title': _('International Transfers'),
        'transfer_type': 'international',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('International Transfers'), 'url': None}
        ]
    }
    return render(request, 'transactions/transfer_list.html', context)


@login_required
def international_transfer_add(request):
    """Add new international transfer"""
    # Get the TRANSFER transaction type for context
    try:
        transaction_type = TransactionType.objects.get(code='TRANSFER', is_deleted=False)
    except TransactionType.DoesNotExist:
        transaction_type = None

    # Get customer parameter from URL if present
    customer_id = request.GET.get('customer')

    context = {
        'page_title': _('New International Transfer'),
        'transfer_type': 'international',
        'transaction_type': transaction_type,
        'transaction_type_code': 'TRANSFER',
        'form_title': _('International Transfer Details'),
        'icon_class': 'globe',
        'back_url': '/transactions/transfer/international/',
        'customer_id': customer_id,  # Pass customer ID to template
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Transactions'), 'url': '/transactions/'},
            {'name': _('Money Transfer'), 'url': '/transactions/transfer/'},
            {'name': _('International Transfers'), 'url': '/transactions/transfer/international/'},
            {'name': _('Add New'), 'url': None}
        ]
    }
    return render(request, 'transactions/forms/international_transfer.html', context)


class TransactionDashboardView(View):
    """Transaction dashboard with statistics and charts."""
    
    @method_decorator(login_required)
    def get(self, request):
        context = {
            'page_title': _('Transaction Dashboard'),
            'breadcrumbs': [
                {'name': _('Dashboard'), 'url': '/'},
                {'name': _('Transaction Dashboard'), 'url': None}
            ]
        }
        return render(request, 'transactions/dashboard.html', context)


@login_required
@require_http_methods(["GET"])
def get_transaction_stats(request):
    """Get transaction statistics for dashboard."""
    try:
        user = request.user
        
        # Base queryset
        transactions = Transaction.objects.filter(is_deleted=False)
        
        # Filter by user's location if not admin
        if not user.can_manage_users() and user.location:
            transactions = transactions.filter(location=user.location)
        
        # Calculate statistics
        stats = {
            'total_transactions': transactions.count(),
            'pending_transactions': transactions.filter(status=Transaction.Status.PENDING).count(),
            'approved_transactions': transactions.filter(status=Transaction.Status.APPROVED).count(),
            'completed_transactions': transactions.filter(status=Transaction.Status.COMPLETED).count(),
            'cancelled_transactions': transactions.filter(status=Transaction.Status.CANCELLED).count(),
        }
        
        # Today's statistics
        from django.utils import timezone
        today = timezone.now().date()
        today_transactions = transactions.filter(created_at__date=today)
        
        stats.update({
            'today_transactions': today_transactions.count(),
            'today_completed': today_transactions.filter(status=Transaction.Status.COMPLETED).count(),
            'today_volume': float(
                today_transactions.filter(status=Transaction.Status.COMPLETED)
                .aggregate(total=models.Sum('from_amount'))['total'] or 0
            ),
        })
        
        return JsonResponse(stats)
        
    except Exception as e:
        logger.error(f"Error getting transaction stats: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_customer_balance(request, customer_id):
    """Get customer balance information."""
    try:
        customer = get_object_or_404(Customer, id=customer_id, is_deleted=False)
        
        # Check if user can view this customer
        if not request.user.can_manage_users() and request.user.location:
            if customer.preferred_location != request.user.location:
                return JsonResponse({'error': 'Permission denied'}, status=403)
        
        # Get balance information
        from apps.core.utils import calculate_customer_balance
        balances = calculate_customer_balance(customer_id)
        
        return JsonResponse({
            'customer': customer.get_display_name(),
            'balances': balances
        })
        
    except Exception as e:
        logger.error(f"Error getting customer balance: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_exchange_rates(request):
    """Get current exchange rates."""
    try:
        location_id = request.GET.get('location_id')
        from_currency = request.GET.get('from_currency')
        to_currency = request.GET.get('to_currency')
        
        # This would get actual exchange rates from the rates API
        # For now, return placeholder data
        rates = {
            'USD_IRR': {'buy': 42000, 'sell': 42500},
            'AED_IRR': {'buy': 11450, 'sell': 11580},
            'USD_AED': {'buy': 3.67, 'sell': 3.68}
        }
        
        return JsonResponse(rates)
        
    except Exception as e:
        logger.error(f"Error getting exchange rates: {e}")
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
@csrf_exempt
def quick_transaction(request):
    """Create a quick transaction (simplified form)."""
    try:
        import json
        data = json.loads(request.body)
        
        # Validate required fields
        required_fields = ['customer_id', 'from_currency_id', 'to_currency_id', 'from_amount', 'exchange_rate']
        for field in required_fields:
            if not data.get(field):
                return JsonResponse({'error': f'Missing required field: {field}'}, status=400)
        
        # Create transaction
        transaction = Transaction.objects.create(
            transaction_type_id=data.get('transaction_type_id'),
            customer_id=data['customer_id'],
            location=request.user.location,
            description=data.get('description', 'Quick transaction'),
            from_currency_id=data['from_currency_id'],
            to_currency_id=data['to_currency_id'],
            from_amount=data['from_amount'],
            to_amount=float(data['from_amount']) * float(data['exchange_rate']),
            exchange_rate=data['exchange_rate'],
            status=Transaction.Status.PENDING,
            created_by=request.user
        )
        
        logger.info(f"Quick transaction created: {transaction.transaction_number} by {request.user}")
        
        return JsonResponse({
            'success': True,
            'transaction_id': str(transaction.id),
            'transaction_number': transaction.transaction_number,
            'message': _('Transaction created successfully')
        })
        
    except Exception as e:
        logger.error(f"Error creating quick transaction: {e}")
        return JsonResponse({'error': str(e)}, status=500)
