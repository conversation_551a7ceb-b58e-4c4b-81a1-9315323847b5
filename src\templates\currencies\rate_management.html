{% extends 'base.html' %}
{% load static %}
{% load i18n %}

{% block title %}{% trans "Exchange Rate Management" %} - Arena Doviz{% endblock %}

{% block extra_css %}
<style>
/* Arena Doviz Design System - Exchange Rate Management */
.rate-management-header {
    background-color: #000d28;
    color: white;
    padding: 2rem 0;
    margin-bottom: 2rem;
}

.rate-card {
    border: 1px solid #dee2e6;
    border-radius: 0;
    margin-bottom: 1.5rem;
    transition: box-shadow 0.2s ease;
    background: white;
}

.rate-card:hover {
    box-shadow: 0 4px 8px rgba(0, 13, 40, 0.1);
}

.rate-card.pending {
    border-left: 4px solid #6a0000;
}

.rate-card.active {
    border-left: 4px solid #013121;
}

.rate-card.rejected {
    border-left: 4px solid #6a0000;
}

.rate-pair {
    font-size: 1.2rem;
    font-weight: bold;
    color: #000d28;
}

.rate-values {
    display: flex;
    justify-content: space-between;
    margin: 1rem 0;
}

.rate-value {
    text-align: center;
    flex: 1;
}

.rate-value .label {
    font-size: 0.8rem;
    color: #6c757d;
    text-transform: uppercase;
    font-weight: 600;
}

.rate-value .value {
    font-size: 1.1rem;
    font-weight: bold;
    color: #000d28;
}

.rate-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.btn-approve {
    background-color: #013121;
    border: 1px solid #013121;
    color: white;
    border-radius: 0;
}

.btn-approve:hover {
    background-color: #024a32;
    border-color: #024a32;
}

.btn-reject {
    background-color: #6a0000;
    border: 1px solid #6a0000;
    color: white;
    border-radius: 0;
}

.btn-reject:hover {
    background-color: #8b0000;
    border-color: #8b0000;
}

.validation-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 0;
    padding: 0.75rem;
    margin: 1rem 0;
}

.validation-error {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 0;
    padding: 0.75rem;
    margin: 1rem 0;
}

/* Arena Doviz Button Styles */
.btn-primary {
    background-color: #000d28;
    border-color: #000d28;
    border-radius: 0;
}

.btn-primary:hover, .btn-primary:focus {
    background-color: #001a3d;
    border-color: #001a3d;
}

.btn-secondary {
    background-color: #6a0000;
    border-color: #6a0000;
    border-radius: 0;
}

.btn-secondary:hover, .btn-secondary:focus {
    background-color: #8b0000;
    border-color: #8b0000;
}

.btn-success {
    background-color: #013121;
    border-color: #013121;
    border-radius: 0;
}

.btn-success:hover, .btn-success:focus {
    background-color: #024a32;
    border-color: #024a32;
}

.btn-outline-primary {
    color: #000d28;
    border-color: #000d28;
    border-radius: 0;
}

.btn-outline-primary:hover {
    background-color: #000d28;
    border-color: #000d28;
}

/* Form Controls */
.form-control, .form-select {
    border-radius: 0;
    border-color: #ced4da;
}

.form-control:focus, .form-select:focus {
    border-color: #000d28;
    box-shadow: 0 0 0 0.2rem rgba(0, 13, 40, 0.25);
}

/* Modal Styles */
.modal-content {
    border-radius: 0;
}

.modal-header {
    background-color: #000d28;
    color: white;
    border-bottom: 1px solid #dee2e6;
}

.modal-header .btn-close {
    filter: invert(1);
}

/* Tab Styles */
.nav-tabs .nav-link {
    border-radius: 0;
    color: #000d28;
    border-color: transparent;
}

.nav-tabs .nav-link.active {
    background-color: #000d28;
    color: white;
    border-color: #000d28;
}

.nav-tabs .nav-link:hover {
    border-color: #000d28;
    color: #000d28;
}

/* Card Styles */
.card {
    border-radius: 0;
    border-color: #dee2e6;
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #dee2e6;
    border-radius: 0;
}

/* Alert Styles */
.alert {
    border-radius: 0;
}

.alert-success {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

.alert-danger {
    background-color: #f8d7da;
    border-color: #f5c6cb;
    color: #721c24;
}

.alert-warning {
    background-color: #fff3cd;
    border-color: #ffeaa7;
    color: #856404;
}

.rate-form {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.recommendations-panel {
    background: #e3f2fd;
    border: 1px solid #bbdefb;
    border-radius: 8px;
    padding: 1rem;
    margin: 1rem 0;
}

.spread-indicator {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    font-weight: bold;
}

.spread-low {
    background: #d4edda;
    color: #155724;
}

.spread-normal {
    background: #cce5ff;
    color: #004085;
}

.spread-high {
    background: #f8d7da;
    color: #721c24;
}
</style>
{% endblock %}

{% block content %}
<div class="rate-management-header">
    <div class="container">
        <div class="row">
            <div class="col-12">
                <h1 class="h2 mb-0">
                    <i class="bi bi-currency-exchange me-2"></i>
                    {% trans "Exchange Rate Management" %}
                </h1>
                <p class="mb-0">{% trans "Manage exchange rates with approval workflow and validation" %}</p>
            </div>
        </div>
    </div>
</div>

<div class="container-fluid">
    <!-- Action Buttons -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary active" onclick="showTab('current')">
                        <i class="bi bi-check-circle me-1"></i>
                        {% trans "Current Rates" %}
                    </button>
                    <button type="button" class="btn btn-outline-warning" onclick="showTab('pending')">
                        <i class="bi bi-clock me-1"></i>
                        {% trans "Pending Approval" %} <span id="pendingCount" class="badge bg-warning">0</span>
                    </button>
                    <button type="button" class="btn btn-outline-info" onclick="showTab('history')">
                        <i class="bi bi-clock-history me-1"></i>
                        {% trans "History" %}
                    </button>
                </div>
                
                <div>
                    <button type="button" class="btn btn-secondary me-2" onclick="testAPI()">
                        <i class="bi bi-bug me-1"></i>
                        Test API
                    </button>
                    <button type="button" class="btn btn-primary" onclick="showCreateRateModal()">
                        <i class="bi bi-plus-circle me-2"></i>
                        {% trans "Create New Rate" %}
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Current Rates Tab -->
    <div id="currentTab" class="tab-content">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-currency-exchange me-2"></i>
                            {% trans "Current Exchange Rates" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="currentRatesContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pending Approval Tab -->
    <div id="pendingTab" class="tab-content" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock me-2"></i>
                            {% trans "Rates Pending Approval" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="pendingRatesContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- History Tab -->
    <div id="historyTab" class="tab-content" style="display: none;">
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h5 class="card-title mb-0">
                            <i class="bi bi-clock-history me-2"></i>
                            {% trans "Rate Change History" %}
                        </h5>
                    </div>
                    <div class="card-body">
                        <div id="historyContainer">
                            <div class="text-center py-4">
                                <div class="spinner-border" role="status">
                                    <span class="visually-hidden">Loading...</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Create Rate Modal -->
<div class="modal fade" id="createRateModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-plus-circle me-2"></i>
                    {% trans "Create New Exchange Rate" %}
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <form id="createRateForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="fromCurrency" class="form-label">{% trans "From Currency" %}</label>
                                <select class="form-select" id="fromCurrency" name="from_currency" required>
                                    <option value="">{% trans "Select currency..." %}</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="toCurrency" class="form-label">{% trans "To Currency" %}</label>
                                <select class="form-select" id="toCurrency" name="to_currency" required>
                                    <option value="">{% trans "Select currency..." %}</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="location" class="form-label">{% trans "Location" %}</label>
                        <select class="form-select" id="location" name="location" required>
                            <option value="">{% trans "Select location..." %}</option>
                        </select>
                    </div>

                    <div class="row">
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="buyRate" class="form-label">{% trans "Buy Rate" %}</label>
                                <input type="number" class="form-control" id="buyRate" name="buy_rate" step="0.000001" required>
                                <div class="form-text">{% trans "Rate at which we buy the currency" %}</div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="mb-3">
                                <label for="sellRate" class="form-label">{% trans "Sell Rate" %}</label>
                                <input type="number" class="form-control" id="sellRate" name="sell_rate" step="0.000001" required>
                                <div class="form-text">{% trans "Rate at which we sell the currency" %}</div>
                            </div>
                        </div>
                    </div>

                    <div class="mb-3">
                        <label for="effectiveFrom" class="form-label">{% trans "Effective From" %}</label>
                        <input type="datetime-local" class="form-control" id="effectiveFrom" name="effective_from">
                        <div class="form-text">{% trans "Leave empty to make effective immediately" %}</div>
                    </div>

                    <div class="mb-3">
                        <label for="notes" class="form-label">{% trans "Notes" %}</label>
                        <textarea class="form-control" id="notes" name="notes" rows="3"></textarea>
                    </div>

                    <!-- Validation Results -->
                    <div id="validationResults"></div>

                    <!-- Recommendations -->
                    <div id="recommendationsPanel" class="recommendations-panel" style="display: none;">
                        <h6><i class="bi bi-lightbulb me-2"></i>{% trans "Recommendations" %}</h6>
                        <div id="recommendationsContent"></div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-info" onclick="validateRate()">
                    <i class="bi bi-check-circle me-2"></i>
                    {% trans "Validate Rate" %}
                </button>
                <button type="button" class="btn btn-primary" onclick="createRate()">
                    <i class="bi bi-save me-2"></i>
                    {% trans "Create Rate" %}
                </button>
            </div>
        </div>
    </div>
</div>

<!-- Approval Modal -->
<div class="modal fade" id="approvalModal" tabindex="-1">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="approvalModalTitle">{% trans "Approve Rate" %}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="approvalRateDetails"></div>
                <div class="mb-3">
                    <label for="approvalNotes" class="form-label">{% trans "Notes" %}</label>
                    <textarea class="form-control" id="approvalNotes" rows="3" placeholder="{% trans 'Optional approval notes...' %}"></textarea>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{% trans "Cancel" %}</button>
                <button type="button" class="btn btn-danger" onclick="rejectRate()">
                    <i class="bi bi-x-circle me-2"></i>
                    {% trans "Reject" %}
                </button>
                <button type="button" class="btn btn-success" onclick="approveRate()">
                    <i class="bi bi-check-circle me-2"></i>
                    {% trans "Approve" %}
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
let currentRateId = null;
let currentTab = 'current';

$(document).ready(function() {
    console.log('Rate management page ready');
    console.log('ArenaDoviz object:', typeof ArenaDoviz);
    console.log('ArenaDoviz.api:', typeof ArenaDoviz?.api);

    // Check if required objects are available
    if (typeof ArenaDoviz === 'undefined') {
        console.error('ArenaDoviz object not found!');
        showAlert('error', 'System error: ArenaDoviz object not loaded');
        return;
    }

    if (typeof ArenaDoviz.api === 'undefined') {
        console.error('ArenaDoviz.api not found!');
        showAlert('error', 'System error: API interface not loaded');
        return;
    }

    loadInitialData();
    loadCurrentRates();

    // Auto-refresh every 5 minutes
    setInterval(function() {
        if (currentTab === 'current') {
            loadCurrentRates();
        } else if (currentTab === 'pending') {
            loadPendingRates();
        }
    }, 300000);
});

function loadInitialData() {
    console.log('Loading initial data...');

    // Load currencies
    console.log('Loading currencies...');
    ArenaDoviz.api.request('GET', 'currencies/currencies/')
        .then(data => {
            console.log('Currencies loaded:', data);
            const fromSelect = $('#fromCurrency');
            const toSelect = $('#toCurrency');

            // Clear existing options except the first one
            fromSelect.find('option:not(:first)').remove();
            toSelect.find('option:not(:first)').remove();

            if (data.results && data.results.length > 0) {
                data.results.forEach(currency => {
                    const option = `<option value="${currency.id}">${currency.code} - ${currency.name}</option>`;
                    fromSelect.append(option);
                    toSelect.append(option);
                });
                console.log(`Loaded ${data.results.length} currencies`);
            } else {
                console.warn('No currencies found');
                showAlert('warning', 'No currencies found. Please add currencies first.');
            }
        })
        .catch(error => {
            console.error('Error loading currencies:', error);
            showAlert('error', 'Failed to load currencies');
        });

    // Load locations
    console.log('Loading locations...');
    ArenaDoviz.api.request('GET', 'locations/locations/')
        .then(data => {
            console.log('Locations loaded:', data);
            const locationSelect = $('#location');

            // Clear existing options except the first one
            locationSelect.find('option:not(:first)').remove();

            if (data.results && data.results.length > 0) {
                data.results.forEach(location => {
                    locationSelect.append(`<option value="${location.id}">${location.name}</option>`);
                });
                console.log(`Loaded ${data.results.length} locations`);
            } else {
                console.warn('No locations found');
                showAlert('warning', 'No locations found. Please add locations first.');
            }
        })
        .catch(error => {
            console.error('Error loading locations:', error);
            showAlert('error', 'Failed to load locations');
        });
}

function showTab(tab) {
    // Update button states
    $('.btn-group button').removeClass('active');
    $(`.btn-group button:contains('${tab === 'current' ? 'Current' : tab === 'pending' ? 'Pending' : 'History'}')`).addClass('active');
    
    // Hide all tabs
    $('.tab-content').hide();
    
    // Show selected tab
    $(`#${tab}Tab`).show();
    
    currentTab = tab;
    
    // Load data for the tab
    if (tab === 'current') {
        loadCurrentRates();
    } else if (tab === 'pending') {
        loadPendingRates();
    } else if (tab === 'history') {
        loadRateHistory();
    }
}

function loadCurrentRates() {
    console.log('Loading current rates...');
    $('#currentRatesContainer').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');

    ArenaDoviz.api.request('GET', 'currencies/rates/?is_active=true')
        .then(data => {
            console.log('Current rates loaded:', data);
            if (data.results && data.results.length > 0) {
                renderRates(data.results, 'currentRatesContainer', false);
            } else {
                $('#currentRatesContainer').html('<div class="alert alert-info">No active exchange rates found. <a href="#" onclick="showCreateRateModal()">Create your first rate</a>.</div>');
            }
        })
        .catch(error => {
            console.error('Error loading current rates:', error);
            $('#currentRatesContainer').html('<div class="alert alert-danger">Failed to load current rates. Please try again.</div>');
        });
}

function loadPendingRates() {
    console.log('Loading pending rates...');
    $('#pendingRatesContainer').html('<div class="text-center"><div class="spinner-border" role="status"><span class="visually-hidden">Loading...</span></div></div>');

    ArenaDoviz.api.request('GET', 'currencies/rates/pending_approval/')
        .then(data => {
            console.log('Pending rates loaded:', data);
            if (data && data.length > 0) {
                renderRates(data, 'pendingRatesContainer', true);
                $('#pendingCount').text(data.length);
            } else {
                $('#pendingRatesContainer').html('<div class="alert alert-info">No pending rates found.</div>');
                $('#pendingCount').text('0');
            }
        })
        .catch(error => {
            console.error('Error loading pending rates:', error);
            $('#pendingRatesContainer').html('<div class="alert alert-danger">Failed to load pending rates. Please try again.</div>');
            $('#pendingCount').text('0');
        });
}

function renderRates(rates, containerId, showApprovalActions) {
    const container = $(`#${containerId}`);
    
    if (rates.length === 0) {
        container.html('<div class="text-center py-4"><p class="text-muted">No rates found</p></div>');
        return;
    }
    
    let html = '';
    
    rates.forEach(rate => {
        const spread = ((rate.sell_rate - rate.buy_rate) / rate.buy_rate * 100).toFixed(3);
        const spreadClass = spread < 0.5 ? 'spread-low' : spread > 2 ? 'spread-high' : 'spread-normal';
        
        html += `
            <div class="rate-card ${rate.is_active ? 'active' : 'pending'}">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-start">
                        <div>
                            <div class="rate-pair">${rate.from_currency_code}/${rate.to_currency_code}</div>
                            <small class="text-muted">${rate.location_name}</small>
                        </div>
                        <div class="text-end">
                            <span class="spread-indicator ${spreadClass}">
                                Spread: ${spread}%
                            </span>
                        </div>
                    </div>
                    
                    <div class="rate-values">
                        <div class="rate-value">
                            <div class="label">Buy Rate</div>
                            <div class="value">${rate.buy_rate}</div>
                        </div>
                        <div class="rate-value">
                            <div class="label">Sell Rate</div>
                            <div class="value">${rate.sell_rate}</div>
                        </div>
                        <div class="rate-value">
                            <div class="label">Effective From</div>
                            <div class="value">${new Date(rate.effective_from).toLocaleDateString()}</div>
                        </div>
                    </div>
                    
                    ${rate.notes ? `<div class="mt-2"><small class="text-muted">${rate.notes}</small></div>` : ''}
                    
                    ${showApprovalActions ? `
                        <div class="rate-actions mt-3">
                            <button class="btn btn-sm btn-approve" onclick="showApprovalModal('${rate.id}', 'approve')">
                                <i class="bi bi-check-circle me-1"></i>
                                Approve
                            </button>
                            <button class="btn btn-sm btn-reject" onclick="showApprovalModal('${rate.id}', 'reject')">
                                <i class="bi bi-x-circle me-1"></i>
                                Reject
                            </button>
                            <button class="btn btn-sm btn-outline-info" onclick="viewRateDetails('${rate.id}')">
                                <i class="bi bi-eye me-1"></i>
                                Details
                            </button>
                        </div>
                    ` : ''}
                </div>
            </div>
        `;
    });
    
    container.html(html);
}

function showCreateRateModal() {
    $('#createRateModal').modal('show');
}

function validateRate() {
    console.log('validateRate() called');

    const formData = {
        from_currency: $('#fromCurrency').val(),
        to_currency: $('#toCurrency').val(),
        location: $('#location').val(),
        buy_rate: $('#buyRate').val(),
        sell_rate: $('#sellRate').val()
    };

    console.log('Form data for validation:', formData);

    // Validate required fields
    if (!formData.from_currency || !formData.to_currency || !formData.location || !formData.buy_rate || !formData.sell_rate) {
        console.error('Missing required fields');
        showAlert('warning', 'Please fill in all required fields before validation');
        return;
    }

    // Validate numeric fields
    if (isNaN(parseFloat(formData.buy_rate)) || isNaN(parseFloat(formData.sell_rate))) {
        console.error('Invalid numeric values');
        showAlert('error', 'Buy rate and sell rate must be valid numbers');
        return;
    }

    // Validate sell rate > buy rate
    if (parseFloat(formData.sell_rate) <= parseFloat(formData.buy_rate)) {
        console.error('Sell rate must be higher than buy rate');
        showAlert('error', 'Sell rate must be higher than buy rate');
        return;
    }

    console.log('Sending validation request...');
    ArenaDoviz.api.request('POST', 'currencies/rates/validate_rate/', formData)
        .then(data => {
            console.log('Validation response:', data);
            displayValidationResults(data);
        })
        .catch(error => {
            console.error('Validation error:', error);
            showAlert('error', `Failed to validate rate: ${error.message || error}`);
        });
}

function displayValidationResults(results) {
    const container = $('#validationResults');
    let html = '';
    
    if (results.errors && results.errors.length > 0) {
        html += '<div class="validation-error">';
        html += '<strong>Errors:</strong><ul>';
        results.errors.forEach(error => {
            html += `<li>${error}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (results.warnings && results.warnings.length > 0) {
        html += '<div class="validation-warning">';
        html += '<strong>Warnings:</strong><ul>';
        results.warnings.forEach(warning => {
            html += `<li>${warning}</li>`;
        });
        html += '</ul></div>';
    }
    
    if (results.requires_approval) {
        html += '<div class="validation-warning">';
        html += '<strong>⚠️ This rate will require approval before becoming active.</strong>';
        html += '</div>';
    }
    
    container.html(html);
}

function createRate() {
    console.log('createRate() called');

    const formData = {
        from_currency: $('#fromCurrency').val(),
        to_currency: $('#toCurrency').val(),
        location: $('#location').val(),
        buy_rate: $('#buyRate').val(),
        sell_rate: $('#sellRate').val(),
        effective_from: $('#effectiveFrom').val() || null,
        notes: $('#notes').val() || ''
    };

    console.log('Form data for creation:', formData);

    // Validate required fields
    if (!formData.from_currency || !formData.to_currency || !formData.location || !formData.buy_rate || !formData.sell_rate) {
        console.error('Missing required fields');
        showAlert('error', 'Please fill in all required fields');
        return;
    }

    // Validate numeric fields
    if (isNaN(parseFloat(formData.buy_rate)) || isNaN(parseFloat(formData.sell_rate))) {
        console.error('Invalid numeric values');
        showAlert('error', 'Buy rate and sell rate must be valid numbers');
        return;
    }

    // Validate sell rate > buy rate
    if (parseFloat(formData.sell_rate) <= parseFloat(formData.buy_rate)) {
        console.error('Sell rate must be higher than buy rate');
        showAlert('error', 'Sell rate must be higher than buy rate');
        return;
    }

    console.log('Sending create request...');
    ArenaDoviz.api.request('POST', 'currencies/rates/create_with_approval/', formData)
        .then(data => {
            console.log('Create response:', data);
            $('#createRateModal').modal('hide');
            showAlert('success', data.message || 'Exchange rate created successfully');

            // Reset form
            $('#createRateForm')[0].reset();
            $('#validationResults').empty();

            // Refresh current tab
            if (currentTab === 'current') {
                loadCurrentRates();
            } else if (currentTab === 'pending') {
                loadPendingRates();
            } else {
                // Default to loading current rates
                loadCurrentRates();
            }
        })
        .catch(error => {
            console.error('Error creating rate:', error);
            let errorMessage = 'Failed to create rate';
            if (error.responseJSON && error.responseJSON.error) {
                errorMessage = error.responseJSON.error;
            } else if (error.message) {
                errorMessage = error.message;
            }
            showAlert('error', errorMessage);
        });
}

function showApprovalModal(rateId, action) {
    currentRateId = rateId;
    
    if (action === 'approve') {
        $('#approvalModalTitle').text('Approve Exchange Rate');
    } else {
        $('#approvalModalTitle').text('Reject Exchange Rate');
    }
    
    $('#approvalModal').modal('show');
}

function approveRate() {
    if (!currentRateId) return;
    
    const notes = $('#approvalNotes').val();
    
    ArenaDoviz.api.request('POST', `currencies/rates/${currentRateId}/approve/`, { notes })
        .then(data => {
            $('#approvalModal').modal('hide');
            showAlert('success', data.message);
            loadPendingRates();
            loadCurrentRates();
        })
        .catch(error => {
            console.error('Error approving rate:', error);
            showAlert('error', 'Failed to approve rate');
        });
}

function rejectRate() {
    if (!currentRateId) return;
    
    const reason = $('#approvalNotes').val() || 'No reason provided';
    
    ArenaDoviz.api.request('POST', `currencies/rates/${currentRateId}/reject/`, { reason })
        .then(data => {
            $('#approvalModal').modal('hide');
            showAlert('success', data.message);
            loadPendingRates();
        })
        .catch(error => {
            console.error('Error rejecting rate:', error);
            showAlert('error', 'Failed to reject rate');
        });
}

function showAlert(type, message) {
    const alertClass = type === 'error' ? 'alert-danger' : type === 'warning' ? 'alert-warning' : 'alert-success';
    const alertHtml = `
        <div class="alert ${alertClass} alert-dismissible fade show position-fixed" style="top: 20px; right: 20px; z-index: 10000;" role="alert">
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    `;
    
    $('body').append(alertHtml);
    
    setTimeout(() => {
        $('.alert').alert('close');
    }, 5000);
}
</script>
{% endblock %}
