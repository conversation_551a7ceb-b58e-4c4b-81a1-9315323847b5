#!/usr/bin/env python
"""
Test script to check API endpoints functionality
"""
import os
import sys
import django
import requests
import json

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Change to src directory for Django setup
os.chdir(src_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings')
django.setup()

from apps.accounts.models import User
from rest_framework.authtoken.models import Token

def get_auth_token():
    """Get authentication token for API requests"""
    try:
        # Get the first admin user
        admin_user = User.objects.filter(is_superuser=True).first()
        if not admin_user:
            print("❌ No admin user found")
            return None
        
        # Get or create token
        token, created = Token.objects.get_or_create(user=admin_user)
        print(f"✅ Using token for user: {admin_user.username}")
        return token.key
    except Exception as e:
        print(f"❌ Error getting auth token: {e}")
        return None

def test_endpoint(url, token, description):
    """Test a single API endpoint"""
    headers = {
        'Authorization': f'Token {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.get(url, headers=headers, timeout=10)
        if response.status_code == 200:
            data = response.json()
            count = len(data.get('results', []))
            print(f"✅ {description}: {count} items")
            return True
        else:
            print(f"❌ {description}: HTTP {response.status_code}")
            print(f"   Response: {response.text[:200]}")
            return False
    except Exception as e:
        print(f"❌ {description}: {e}")
        return False

def main():
    print("🔍 Testing Arena Doviz API Endpoints")
    print("=" * 50)
    
    # Get authentication token
    token = get_auth_token()
    if not token:
        return
    
    base_url = "http://localhost:8000/api/v1"
    
    # Test endpoints
    endpoints = [
        (f"{base_url}/customers/customers/", "Customers API"),
        (f"{base_url}/locations/locations/", "Locations API"),
        (f"{base_url}/currencies/currencies/", "Currencies API"),
        (f"{base_url}/transactions/types/", "Transaction Types API"),
        (f"{base_url}/transactions/transactions/", "Transactions API"),
    ]
    
    results = []
    for url, description in endpoints:
        result = test_endpoint(url, token, description)
        results.append((description, result))
    
    print("\n" + "=" * 50)
    print("📊 Summary:")
    for description, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"   {status} {description}")
    
    # Test specific transaction type filtering
    print("\n🔍 Testing Transaction Type Filtering:")
    try:
        # Test filtering by transaction type code
        test_codes = ['EXCHANGE', 'TRANSFER', 'DEPOSIT', 'WITHDRAWAL']
        for code in test_codes:
            url = f"{base_url}/transactions/transactions/?transaction_type_code={code}"
            test_endpoint(url, token, f"Transactions filtered by {code}")
    except Exception as e:
        print(f"❌ Error testing transaction filtering: {e}")

if __name__ == "__main__":
    main()
