<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Arena Doviz Authentication Debug</title>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
</head>
<body>
    <h1>Arena Doviz Authentication Debug</h1>
    <div id="debug-output"></div>
    
    <script>
        // Simple debug script to check authentication
        function debugAuth() {
            const output = document.getElementById('debug-output');
            let html = '<h2>Authentication Debug Results:</h2>';
            
            // Check localStorage tokens
            const accessToken = localStorage.getItem('arena_access_token');
            const refreshToken = localStorage.getItem('arena_refresh_token');
            const userData = localStorage.getItem('arena_user_data');
            
            html += '<h3>Local Storage:</h3>';
            html += `<p>Access Token: ${accessToken ? 'Present (' + accessToken.substring(0, 20) + '...)' : 'Missing'}</p>`;
            html += `<p>Refresh Token: ${refreshToken ? 'Present (' + refreshToken.substring(0, 20) + '...)' : 'Missing'}</p>`;
            html += `<p>User Data: ${userData ? 'Present' : 'Missing'}</p>`;
            
            // Test API endpoints
            html += '<h3>API Tests:</h3>';
            
            // Test without auth
            fetch('/api/v1/customers/customers/')
                .then(response => {
                    html += `<p>Customers API (no auth): ${response.status} ${response.statusText}</p>`;
                    output.innerHTML = html;
                })
                .catch(error => {
                    html += `<p>Customers API (no auth): Error - ${error}</p>`;
                    output.innerHTML = html;
                });
            
            // Test with JWT token if available
            if (accessToken) {
                fetch('/api/v1/customers/customers/', {
                    headers: {
                        'Authorization': 'Bearer ' + accessToken
                    }
                })
                .then(response => response.json())
                .then(data => {
                    html += `<p>Customers API (with JWT): Success - ${data.results ? data.results.length : 0} customers</p>`;
                    output.innerHTML = html;
                })
                .catch(error => {
                    html += `<p>Customers API (with JWT): Error - ${error}</p>`;
                    output.innerHTML = html;
                });
            }
            
            // Test session-based JWT token retrieval
            html += '<h3>JWT Token Retrieval Test:</h3>';
            fetch('/api/v1/accounts/users/get_jwt_tokens/', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'X-CSRFToken': getCookie('csrftoken')
                },
                credentials: 'include'
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
            })
            .then(data => {
                html += `<p>JWT Token Retrieval: Success</p>`;
                html += `<p>New Access Token: ${data.access.substring(0, 20)}...</p>`;
                
                // Store the new tokens
                localStorage.setItem('arena_access_token', data.access);
                localStorage.setItem('arena_refresh_token', data.refresh);
                localStorage.setItem('arena_user_data', JSON.stringify(data.user));
                
                html += `<p>Tokens stored in localStorage</p>`;
                output.innerHTML = html;
                
                // Now test API with new token
                fetch('/api/v1/customers/customers/', {
                    headers: {
                        'Authorization': 'Bearer ' + data.access
                    }
                })
                .then(response => response.json())
                .then(apiData => {
                    html += `<p>Customers API (with new JWT): Success - ${apiData.results ? apiData.results.length : 0} customers</p>`;
                    output.innerHTML = html;
                })
                .catch(error => {
                    html += `<p>Customers API (with new JWT): Error - ${error}</p>`;
                    output.innerHTML = html;
                });
            })
            .catch(error => {
                html += `<p>JWT Token Retrieval: Error - ${error}</p>`;
                output.innerHTML = html;
            });
            
            output.innerHTML = html;
        }
        
        // Helper function to get CSRF token
        function getCookie(name) {
            let cookieValue = null;
            if (document.cookie && document.cookie !== '') {
                const cookies = document.cookie.split(';');
                for (let i = 0; i < cookies.length; i++) {
                    const cookie = cookies[i].trim();
                    if (cookie.substring(0, name.length + 1) === (name + '=')) {
                        cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                        break;
                    }
                }
            }
            return cookieValue;
        }
        
        // Run debug on page load
        window.onload = debugAuth;
    </script>
</body>
</html>
