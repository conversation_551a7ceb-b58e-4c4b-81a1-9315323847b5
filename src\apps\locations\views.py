"""
API views for Arena Doviz Locations app.
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.db.models import Count
from django.utils import timezone
from .models import Location, LocationSettings
from .serializers import (
    LocationSerializer, LocationListSerializer, LocationCreateSerializer,
    LocationSettingsSerializer, LocationStatsSerializer, LocationBalanceSerializer
)
from apps.core.utils import log_user_action, get_client_ip, format_currency
import logging

logger = logging.getLogger(__name__)


class LocationViewSet(viewsets.ModelViewSet):
    """ViewSet for Location management."""
    
    queryset = Location.objects.filter(is_deleted=False)
    permission_classes = [permissions.IsAuthenticated]
    ordering = ['sort_order', 'name']
    
    def get_serializer_class(self):
        """Return appropriate serializer based on action."""
        if self.action == 'list':
            return LocationListSerializer
        elif self.action == 'create':
            return LocationCreateSerializer
        return LocationSerializer
    
    def get_queryset(self):
        """Filter queryset based on query parameters."""
        queryset = self.queryset
        
        # Filter by active status
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # Filter by country
        country = self.request.query_params.get('country')
        if country:
            queryset = queryset.filter(country__icontains=country)
        
        # Filter by main office
        is_main_office = self.request.query_params.get('is_main_office')
        if is_main_office is not None:
            queryset = queryset.filter(is_main_office=is_main_office.lower() == 'true')
        
        return queryset.select_related('manager')
    
    def perform_create(self, serializer):
        """Create location with audit logging."""
        logger.info(f"Creating location with data: {serializer.validated_data}")
        logger.info(f"User creating location: {self.request.user}")

        try:
            location = serializer.save()
            logger.info(f"Location saved successfully: {location}")

            # Create default settings for the location
            LocationSettings.objects.create(location=location)
            logger.info(f"Location settings created for: {location.code}")

            log_user_action(
                user=self.request.user,
                action='create',
                model_name='Location',
                object_id=str(location.id),
                object_repr=str(location),
                ip_address=get_client_ip(self.request)
            )

            logger.info(f"Location created successfully: {location.code} by {self.request.user.username}")

        except Exception as e:
            logger.error(f"Error creating location: {str(e)}")
            logger.exception("Full exception details:")
            raise
    
    def perform_update(self, serializer):
        """Update location with audit logging."""
        old_instance = self.get_object()
        location = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='update',
            model_name='Location',
            object_id=str(location.id),
            object_repr=str(location),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Location updated: {location.code} by {self.request.user.username}")
    
    def perform_destroy(self, instance):
        """Soft delete location with audit logging."""
        instance.delete(user=self.request.user)
        
        log_user_action(
            user=self.request.user,
            action='delete',
            model_name='Location',
            object_id=str(instance.id),
            object_repr=str(instance),
            ip_address=get_client_ip(self.request)
        )
        
        logger.info(f"Location deleted: {instance.code} by {self.request.user.username}")
    
    @action(detail=True, methods=['get'])
    def balance(self, request, pk=None):
        """Get location balance summary."""
        location = self.get_object()
        
        try:
            balance_summary = location.get_balances_summary()
            
            # Format balance data
            balance_data = []
            for currency_code, balance in balance_summary.items():
                balance_data.append({
                    'location_id': location.id,
                    'location_name': location.name,
                    'location_code': location.code,
                    'currency_code': currency_code,
                    'balance': balance,
                    'formatted_balance': format_currency(balance, currency_code),
                    'last_updated': timezone.now()
                })
            
            serializer = LocationBalanceSerializer(balance_data, many=True)
            return Response(serializer.data)
            
        except Exception as e:
            logger.error(f"Error getting balance for location {location.code}: {e}")
            return Response(
                {'error': 'Failed to retrieve balance information'},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate location."""
        location = self.get_object()
        
        if not location.is_active:
            location.is_active = True
            location.save(update_fields=['is_active'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Location',
                object_id=str(location.id),
                object_repr=str(location),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'activated'}
            )
            
            return Response({'message': 'Location activated successfully'})
        
        return Response({'message': 'Location is already active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate location."""
        location = self.get_object()
        
        if location.is_active:
            # Check if this is the main office
            if location.is_main_office:
                return Response(
                    {'message': 'Cannot deactivate main office location'},
                    status=status.HTTP_400_BAD_REQUEST
                )
            
            location.is_active = False
            location.save(update_fields=['is_active'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Location',
                object_id=str(location.id),
                object_repr=str(location),
                ip_address=get_client_ip(request),
                additional_data={'status_changed': 'deactivated'}
            )
            
            return Response({'message': 'Location deactivated successfully'})
        
        return Response({'message': 'Location is not active'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=True, methods=['post'])
    def set_main_office(self, request, pk=None):
        """Set location as main office."""
        location = self.get_object()
        
        if not location.is_main_office:
            # Remove main office status from other locations
            Location.objects.filter(is_main_office=True).update(is_main_office=False)
            
            # Set this location as main office
            location.is_main_office = True
            location.save(update_fields=['is_main_office'])
            
            log_user_action(
                user=request.user,
                action='update',
                model_name='Location',
                object_id=str(location.id),
                object_repr=str(location),
                ip_address=get_client_ip(request),
                additional_data={'main_office_changed': True}
            )
            
            return Response({'message': 'Location set as main office successfully'})
        
        return Response({'message': 'Location is already the main office'}, status=status.HTTP_400_BAD_REQUEST)
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get location statistics."""
        # Calculate statistics
        total_locations = Location.objects.filter(is_deleted=False).count()
        active_locations = Location.objects.filter(is_active=True, is_deleted=False).count()
        
        # Locations by country
        locations_by_country = dict(
            Location.objects.filter(is_deleted=False)
            .values('country')
            .annotate(count=Count('id'))
            .values_list('country', 'count')
        )
        
        # Main office
        main_office = Location.get_main_office()
        main_office_name = main_office.name if main_office else 'Not set'
        
        # Operating locations (currently open)
        operating_locations = sum(
            1 for location in Location.objects.filter(is_active=True, is_deleted=False)
            if location.is_operating_now()
        )
        
        # Total users across all locations
        total_users = sum(
            location.get_active_users_count()
            for location in Location.objects.filter(is_active=True, is_deleted=False)
        )
        
        # Total transactions today (placeholder - would need actual transaction data)
        total_transactions_today = 0  # TODO: Implement actual calculation
        
        stats_data = {
            'total_locations': total_locations,
            'active_locations': active_locations,
            'locations_by_country': locations_by_country,
            'main_office': main_office_name,
            'operating_locations': operating_locations,
            'total_users': total_users,
            'total_transactions_today': total_transactions_today
        }
        
        serializer = LocationStatsSerializer(stats_data)
        return Response(serializer.data)
    
    @action(detail=False, methods=['get'])
    def active(self, request):
        """Get all active locations."""
        active_locations = Location.get_active_locations()
        serializer = LocationListSerializer(active_locations, many=True)
        return Response(serializer.data)


class LocationSettingsViewSet(viewsets.ModelViewSet):
    """ViewSet for LocationSettings management."""
    
    queryset = LocationSettings.objects.filter(is_deleted=False)
    serializer_class = LocationSettingsSerializer
    permission_classes = [permissions.IsAuthenticated]
    
    def get_queryset(self):
        """Filter settings by location if specified."""
        queryset = self.queryset
        
        location_id = self.request.query_params.get('location')
        if location_id:
            queryset = queryset.filter(location_id=location_id)
        
        return queryset.select_related('location')
    
    def perform_create(self, serializer):
        """Create settings with audit logging."""
        settings = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='create',
            model_name='LocationSettings',
            object_id=str(settings.id),
            object_repr=str(settings),
            ip_address=get_client_ip(self.request)
        )
    
    def perform_update(self, serializer):
        """Update settings with audit logging."""
        settings = serializer.save()
        
        log_user_action(
            user=self.request.user,
            action='update',
            model_name='LocationSettings',
            object_id=str(settings.id),
            object_repr=str(settings),
            ip_address=get_client_ip(self.request)
        )
    
    @action(detail=True, methods=['post'])
    def test_ip_access(self, request, pk=None):
        """Test if current IP is allowed."""
        settings = self.get_object()
        client_ip = get_client_ip(request)
        
        is_allowed = settings.is_ip_allowed(client_ip)
        
        return Response({
            'ip_address': client_ip,
            'is_allowed': is_allowed,
            'allowed_ips': settings.get_allowed_ips()
        })
