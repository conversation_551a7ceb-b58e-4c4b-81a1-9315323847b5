#!/usr/bin/env python
"""
Create admin user for Arena Doviz
"""
import os
import sys
import django
from pathlib import Path

# Set environment variables for PostgreSQL
os.environ['DB_NAME'] = 'arena_doviz_prod'
os.environ['DB_USER'] = 'postgres'
os.environ['DB_PASSWORD'] = 'Pamir2600!'
os.environ['DB_HOST'] = 'localhost'
os.environ['DB_PORT'] = '5432'
os.environ['DJANGO_SETTINGS_MODULE'] = 'config.settings.prod'
os.environ['ARENA_ENCRYPTION_KEY'] = 'x6XI010TzdY3oaN9cCgW1Z81Kb956ZXCS0ZBdPrCgoo='

# Add src directory to Python path
src_path = Path(__file__).parent / 'src'
sys.path.insert(0, str(src_path))

# Setup Django
django.setup()

from django.contrib.auth import get_user_model
from django.contrib.auth.models import Group

def create_admin_user():
    """Create admin user if it doesn't exist"""
    User = get_user_model()
    
    print("🔍 Checking existing users...")
    
    # List existing users
    users = User.objects.all()
    if users.exists():
        print("Existing users:")
        for user in users:
            print(f"- {user.username} ({user.email}) - Active: {user.is_active}, Staff: {user.is_staff}, Superuser: {user.is_superuser}")
    else:
        print("No users found in database")
    
    # Check if admin_user already exists
    if User.objects.filter(username='admin_user').exists():
        print("✅ admin_user already exists")
        return True
    
    print("\n🔧 Creating admin_user...")
    
    try:
        # Create the admin user
        admin_user = User.objects.create_user(
            username='admin_user',
            email='<EMAIL>',
            password='Admin123!@#',
            first_name='Admin',
            last_name='User',
            is_staff=True,
            is_superuser=True,
            is_active=True
        )
        
        # Add to Admin group
        admin_group, created = Group.objects.get_or_create(name='Admin')
        admin_user.groups.add(admin_group)
        
        print(f"✅ Admin user created successfully!")
        print(f"   Username: admin_user")
        print(f"   Email: <EMAIL>")
        print(f"   Password: Admin123!@#")
        print(f"   Groups: {', '.join([g.name for g in admin_user.groups.all()])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return False

def create_test_users():
    """Create additional test users"""
    User = get_user_model()
    
    test_users = [
        {
            'username': 'accountant_user',
            'email': '<EMAIL>',
            'password': 'Account123!',
            'first_name': 'Accountant',
            'last_name': 'User',
            'group': 'Accountant'
        },
        {
            'username': 'branch_user',
            'email': '<EMAIL>',
            'password': 'Branch123!',
            'first_name': 'Branch',
            'last_name': 'Employee',
            'group': 'Branch Employee'
        }
    ]
    
    print("\n🔧 Creating test users...")
    
    for user_data in test_users:
        username = user_data['username']
        
        if User.objects.filter(username=username).exists():
            print(f"✅ {username} already exists")
            continue
        
        try:
            user = User.objects.create_user(
                username=user_data['username'],
                email=user_data['email'],
                password=user_data['password'],
                first_name=user_data['first_name'],
                last_name=user_data['last_name'],
                is_staff=True,
                is_active=True
            )
            
            # Add to appropriate group
            group, created = Group.objects.get_or_create(name=user_data['group'])
            user.groups.add(group)
            
            print(f"✅ Created user: {username} ({user_data['group']})")
            
        except Exception as e:
            print(f"❌ Error creating {username}: {e}")

def main():
    """Main function"""
    print("Arena Doviz Admin User Creation")
    print("="*50)
    
    # Create admin user
    admin_success = create_admin_user()
    
    if admin_success:
        # Create test users
        create_test_users()
        
        print("\n🎉 User creation completed!")
        print("\n📋 Login Credentials:")
        print("Admin User:")
        print("  Username: admin_user")
        print("  Password: Admin123!@#")
        print("\nAccountant User:")
        print("  Username: accountant_user")
        print("  Password: Account123!")
        print("\nBranch Employee:")
        print("  Username: branch_user")
        print("  Password: Branch123!")
        
        print("\n🚀 You can now login to the system at:")
        print("   http://localhost:8000")
        
        return True
    else:
        print("\n❌ Admin user creation failed!")
        return False

if __name__ == '__main__':
    success = main()
    input("\nPress Enter to exit...")
    sys.exit(0 if success else 1)
