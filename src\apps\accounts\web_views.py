"""
Web views for Arena Doviz Accounts app - User Management.
"""

from django.shortcuts import render, redirect
from django.contrib.auth.decorators import login_required
from django.contrib import messages
from django.utils.translation import gettext_lazy as _
from django.http import JsonResponse
from django.views.decorators.http import require_http_methods
from django.views.decorators.csrf import csrf_exempt
from django.utils.decorators import method_decorator
from django.views import View
from django.core.paginator import Paginator
from django.db.models import Q, Count
from django.utils import timezone
from datetime import timed<PERSON>ta
import json

from .models import User, UserSession
from apps.locations.models import Location
from apps.core.utils import log_user_action, get_client_ip


@login_required
def user_management(request):
    """User management page with list and statistics."""
    # Check if user can manage users
    if not request.user.can_manage_users():
        messages.error(request, _('You do not have permission to manage users.'))
        return redirect('core_web:dashboard')
    
    context = {
        'page_title': _('User Management'),
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Administration'), 'url': None},
            {'name': _('User Management'), 'url': None}
        ]
    }
    return render(request, 'accounts/user_management.html', context)


@login_required
def user_detail(request, user_id):
    """User detail page."""
    # Check if user can manage users or viewing own profile
    if not request.user.can_manage_users() and str(request.user.id) != user_id:
        messages.error(request, _('You do not have permission to view this user.'))
        return redirect('core_web:dashboard')
    
    try:
        user = User.objects.get(id=user_id, is_deleted=False)
    except User.DoesNotExist:
        messages.error(request, _('User not found.'))
        return redirect('accounts_web:user_management')
    
    context = {
        'user_obj': user,  # Using user_obj to avoid conflict with request.user
        'page_title': f'{user.get_display_name()} - {_("User Details")}',
        'breadcrumbs': [
            {'name': _('Dashboard'), 'url': '/'},
            {'name': _('Administration'), 'url': None},
            {'name': _('User Management'), 'url': '/users/'},
            {'name': user.get_display_name(), 'url': None}
        ]
    }
    return render(request, 'accounts/user_detail.html', context)


@login_required
@require_http_methods(["GET"])
def get_user_stats(request):
    """Get user statistics for dashboard."""
    if not request.user.can_manage_users():
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        # Calculate statistics
        total_users = User.objects.filter(is_deleted=False).count()
        active_users = User.objects.filter(is_active=True, is_deleted=False).count()
        inactive_users = total_users - active_users
        
        # Users by role
        users_by_role = dict(
            User.objects.filter(is_deleted=False)
            .values('role')
            .annotate(count=Count('id'))
            .values_list('role', 'count')
        )
        
        # Users by location
        users_by_location = dict(
            User.objects.filter(is_deleted=False, location__isnull=False)
            .values('location__name')
            .annotate(count=Count('id'))
            .values_list('location__name', 'count')
        )
        
        # Recent activity (last 24 hours)
        yesterday = timezone.now() - timedelta(days=1)
        recent_logins = User.objects.filter(
            last_login__gte=yesterday,
            is_deleted=False
        ).count()
        
        # Active sessions
        active_sessions = UserSession.objects.filter(is_active=True).count()
        
        # Failed login attempts
        failed_attempts = User.objects.filter(
            failed_login_attempts__gt=0,
            is_deleted=False
        ).count()
        
        # Locked accounts
        locked_accounts = User.objects.filter(
            account_locked_until__isnull=False,
            account_locked_until__gt=timezone.now(),
            is_deleted=False
        ).count()
        
        return JsonResponse({
            'total_users': total_users,
            'active_users': active_users,
            'inactive_users': inactive_users,
            'users_by_role': users_by_role,
            'users_by_location': users_by_location,
            'recent_logins': recent_logins,
            'active_sessions': active_sessions,
            'failed_attempts': failed_attempts,
            'locked_accounts': locked_accounts
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_user_list(request):
    """Get paginated user list for DataTables."""
    if not request.user.can_manage_users():
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        # Get query parameters
        draw = int(request.GET.get('draw', 1))
        start = int(request.GET.get('start', 0))
        length = int(request.GET.get('length', 10))
        search_value = request.GET.get('search[value]', '')
        order_column = int(request.GET.get('order[0][column]', 0))
        order_dir = request.GET.get('order[0][dir]', 'asc')
        
        # Filter parameters
        role_filter = request.GET.get('role', '')
        location_filter = request.GET.get('location', '')
        status_filter = request.GET.get('status', '')
        
        # Base queryset
        queryset = User.objects.filter(is_deleted=False).select_related('location')
        
        # Apply filters
        if role_filter:
            queryset = queryset.filter(role=role_filter)
        
        if location_filter:
            queryset = queryset.filter(location_id=location_filter)
        
        if status_filter == 'active':
            queryset = queryset.filter(is_active=True)
        elif status_filter == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status_filter == 'locked':
            queryset = queryset.filter(
                account_locked_until__isnull=False,
                account_locked_until__gt=timezone.now()
            )
        
        # Apply search
        if search_value:
            queryset = queryset.filter(
                Q(username__icontains=search_value) |
                Q(first_name__icontains=search_value) |
                Q(last_name__icontains=search_value) |
                Q(email__icontains=search_value) |
                Q(employee_id__icontains=search_value)
            )
        
        # Apply ordering
        columns = ['username', 'first_name', 'last_name', 'email', 'role', 'location__name', 'is_active', 'date_joined']
        if order_column < len(columns):
            order_field = columns[order_column]
            if order_dir == 'desc':
                order_field = '-' + order_field
            queryset = queryset.order_by(order_field)
        
        # Get total count
        total_records = User.objects.filter(is_deleted=False).count()
        filtered_records = queryset.count()
        
        # Apply pagination
        users = queryset[start:start + length]
        
        # Format data for DataTables
        data = []
        for user in users:
            data.append({
                'id': str(user.id),
                'username': user.username,
                'first_name': user.first_name,
                'last_name': user.last_name,
                'email': user.email,
                'role': user.get_role_display(),
                'role_code': user.role,
                'location': user.location.name if user.location else '-',
                'location_id': str(user.location.id) if user.location else None,
                'is_active': user.is_active,
                'date_joined': user.date_joined.strftime('%Y-%m-%d %H:%M'),
                'last_login': user.last_login.strftime('%Y-%m-%d %H:%M') if user.last_login else '-',
                'employee_id': user.employee_id or '-',
                'phone_number': user.phone_number or '-',
                'is_locked': user.is_account_locked(),
                'failed_attempts': user.failed_login_attempts
            })
        
        return JsonResponse({
            'draw': draw,
            'recordsTotal': total_records,
            'recordsFiltered': filtered_records,
            'data': data
        })
        
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["GET"])
def get_locations_for_select(request):
    """Get locations for select dropdown."""
    if not request.user.can_manage_users():
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        locations = Location.objects.filter(is_deleted=False, is_active=True).order_by('name')
        data = [{'id': str(loc.id), 'name': loc.name} for loc in locations]
        return JsonResponse({'locations': data})
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)


@login_required
@require_http_methods(["POST"])
def unlock_user_account(request, user_id):
    """Unlock a user account."""
    if not request.user.can_manage_users():
        return JsonResponse({'error': 'Permission denied'}, status=403)
    
    try:
        user = User.objects.get(id=user_id, is_deleted=False)
        user.reset_failed_login_attempts()
        
        # Log the action
        log_user_action(
            user=request.user,
            action='unlock_account',
            model_name='User',
            object_id=str(user.id),
            object_repr=str(user),
            ip_address=get_client_ip(request)
        )
        
        return JsonResponse({'message': _('User account unlocked successfully.')})
        
    except User.DoesNotExist:
        return JsonResponse({'error': _('User not found.')}, status=404)
    except Exception as e:
        return JsonResponse({'error': str(e)}, status=500)
