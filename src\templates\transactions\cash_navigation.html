{% extends 'base.html' %}
{% load i18n %}

{% block title %}{% trans "Cash Transactions" %} - Arena Doviz{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1 class="h3 mb-0">
                    <i class="bi bi-cash"></i>
                    {% trans "Cash Transactions" %}
                </h1>
                <nav aria-label="breadcrumb">
                    <ol class="breadcrumb">
                        <li class="breadcrumb-item"><a href="{% url 'core:dashboard' %}">{% trans "Dashboard" %}</a></li>
                        <li class="breadcrumb-item"><a href="{% url 'transactions_web:navigation' %}">{% trans "Transactions" %}</a></li>
                        <li class="breadcrumb-item active">{% trans "Cash Transactions" %}</li>
                    </ol>
                </nav>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Cash Deposit -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header text-white" style="background-color: #000d28;">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-cash-coin"></i>
                        {% trans "Cash Deposit" %}
                    </h5>
                </div>
                <div class="card-body d-flex flex-column">
                    <p class="card-text">{% trans "Accept cash deposits from customers to their account balances with verification and documentation." %}</p>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">{% trans "Features:" %}</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Customer verification" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Cash counting verification" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Receipt generation" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Document upload" %}</li>
                        </ul>
                    </div>
                    
                    <div class="mt-auto">
                        <a href="{% url 'transactions_web:type_list' 'DEPOSIT' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list"></i> {% trans "View List" %}
                        </a>
                        <a href="{% url 'transactions_web:type_add' 'DEPOSIT' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> {% trans "New Deposit" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cash Withdrawal -->
        <div class="col-lg-6 col-md-12 mb-4">
            <div class="card h-100">
                <div class="card-header text-white" style="background-color: #000d28;">
                    <h5 class="card-title mb-0">
                        <i class="bi bi-cash-stack"></i>
                        {% trans "Cash Withdrawal" %}
                    </h5>
                </div>
                <div class="card-body d-flex flex-column">
                    <p class="card-text">{% trans "Process cash withdrawals from customer accounts with authorization, verification, and courier integration." %}</p>
                    
                    <div class="mb-3">
                        <h6 class="text-muted">{% trans "Features:" %}</h6>
                        <ul class="list-unstyled">
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Balance verification" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "ID and signature verification" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Courier delivery option" %}</li>
                            <li><i class="bi bi-check-circle text-success"></i> {% trans "Authorization workflow" %}</li>
                        </ul>
                    </div>
                    
                    <div class="mt-auto">
                        <a href="{% url 'transactions_web:type_list' 'WITHDRAWAL' %}" class="btn btn-outline-primary me-2">
                            <i class="bi bi-list"></i> {% trans "View List" %}
                        </a>
                        <a href="{% url 'transactions_web:type_add' 'WITHDRAWAL' %}" class="btn btn-primary">
                            <i class="bi bi-plus"></i> {% trans "New Withdrawal" %}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Quick Stats Row -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h6 class="card-title mb-0">
                        <i class="bi bi-graph-up"></i>
                        {% trans "Cash Transaction Statistics" %}
                    </h6>
                </div>
                <div class="card-body">
                    <div class="row text-center">
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-success" id="total-deposits">-</h4>
                                <small class="text-muted">{% trans "Total Deposits" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-warning" id="total-withdrawals">-</h4>
                                <small class="text-muted">{% trans "Total Withdrawals" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="border-end">
                                <h4 class="text-info" id="pending-approvals">-</h4>
                                <small class="text-muted">{% trans "Pending Approvals" %}</small>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <h4 class="text-primary" id="today-transactions">-</h4>
                            <small class="text-muted">{% trans "Today's Transactions" %}</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
$(document).ready(function() {
    // Load cash transaction statistics
    loadCashStats();
});

function loadCashStats() {
    // Load deposit count
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        },
        data: {
            transaction_type_code: 'DEPOSIT',
            page_size: 1
        },
        success: function(data) {
            $('#total-deposits').text(data.count || 0);
        }
    });

    // Load withdrawal count
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        },
        data: {
            transaction_type_code: 'WITHDRAWAL',
            page_size: 1
        },
        success: function(data) {
            $('#total-withdrawals').text(data.count || 0);
        }
    });

    // Load pending approvals
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        },
        data: {
            status: 'pending',
            transaction_type__code__in: 'DEPOSIT,WITHDRAWAL',
            page_size: 1
        },
        success: function(data) {
            $('#pending-approvals').text(data.count || 0);
        }
    });

    // Load today's transactions
    const today = new Date().toISOString().split('T')[0];
    $.ajax({
        url: '/api/v1/transactions/transactions/',
        method: 'GET',
        headers: {
            'Authorization': 'Bearer ' + ArenaDoviz.auth.getAccessToken(),
            'Content-Type': 'application/json'
        },
        data: {
            created_at__date: today,
            transaction_type__code__in: 'DEPOSIT,WITHDRAWAL',
            page_size: 1
        },
        success: function(data) {
            $('#today-transactions').text(data.count || 0);
        }
    });
}
</script>
{% endblock %}
