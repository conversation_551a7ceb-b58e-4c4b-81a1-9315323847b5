"""
Custom SQLite database backend for Arena Doviz with optimized settings.
"""

from django.db.backends.sqlite3.base import DatabaseWrapper as BaseDatabaseWrapper


class DatabaseWrapper(BaseDatabaseWrapper):
    """Custom SQLite database wrapper with optimized settings for production."""
    
    def get_new_connection(self, conn_params):
        """Create a new database connection with optimized SQLite settings."""
        conn = super().get_new_connection(conn_params)
        
        # Configure SQLite for better concurrency and performance
        with conn:
            # Enable WAL mode for better concurrency
            conn.execute('PRAGMA journal_mode=WAL;')
            
            # Set synchronous mode to NORMAL for better performance
            conn.execute('PRAGMA synchronous=NORMAL;')
            
            # Increase cache size for better performance
            conn.execute('PRAGMA cache_size=10000;')
            
            # Store temporary tables in memory
            conn.execute('PRAGMA temp_store=MEMORY;')
            
            # Set busy timeout to 30 seconds
            conn.execute('PRAGMA busy_timeout=30000;')
            
            # Enable foreign key constraints
            conn.execute('PRAGMA foreign_keys=ON;')
            
            # Optimize for write-heavy workloads
            conn.execute('PRAGMA wal_autocheckpoint=1000;')
            
        return conn
