#!/usr/bin/env python
"""
Test script to verify commission calculation fix
"""
import os
import sys
import django
from decimal import Decimal

# Add the src directory to Python path
src_path = os.path.join(os.path.dirname(__file__), 'src')
sys.path.insert(0, src_path)

# Change to src directory for Django setup
os.chdir(src_path)

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'config.settings.dev')
django.setup()

from apps.transactions.commission_utils import commission_calculator
from apps.transactions.models import TransactionType
from apps.locations.models import Location
from apps.currencies.models import Currency

def test_commission_calculation():
    """Test commission calculation with TempTransaction"""
    print("🔍 Testing Commission Calculation Fix")
    print("=" * 50)
    
    try:
        # Get test data
        location = Location.objects.filter(is_deleted=False).first()
        transaction_type = TransactionType.objects.filter(code='EXCHANGE', is_deleted=False).first()
        usd = Currency.objects.filter(code='USD', is_deleted=False).first()
        aed = Currency.objects.filter(code='AED', is_deleted=False).first()
        
        if not all([location, transaction_type, usd, aed]):
            print("❌ Missing required test data")
            return False
        
        # Test data for commission calculation
        transaction_data = {
            'location': location,
            'transaction_type': transaction_type,
            'from_currency': usd,
            'to_currency': aed,
            'from_amount': Decimal('1000.00'),
            'delivery_method': 'cash'
        }
        
        print(f"📊 Testing commission calculation with:")
        print(f"   Location: {location.name}")
        print(f"   Type: {transaction_type.name}")
        print(f"   From: {usd.code}")
        print(f"   To: {aed.code}")
        print(f"   Amount: {transaction_data['from_amount']}")
        print(f"   Delivery: {transaction_data['delivery_method']}")
        
        # Test commission preview (this was causing the error)
        result = commission_calculator.get_commission_preview(transaction_data)
        
        print(f"\n✅ Commission calculation successful!")
        print(f"   Commission Amount: {result['amount']}")
        print(f"   Commission Currency: {result['currency'].code if result['currency'] else 'None'}")
        print(f"   Rule Applied: {result['rule'].name if result['rule'] else 'Default'}")
        
        if 'breakdown' in result:
            print(f"   Breakdown: {result['breakdown']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Commission calculation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_method_access():
    """Test that TempTransaction can access DeliveryMethod"""
    print("\n🔍 Testing DeliveryMethod Access")
    print("=" * 50)
    
    try:
        from apps.transactions.commission_utils import CommissionCalculator
        
        # Create a test transaction data
        transaction_data = {
            'location': Location.objects.filter(is_deleted=False).first(),
            'transaction_type': TransactionType.objects.filter(is_deleted=False).first(),
            'from_currency': Currency.objects.filter(is_deleted=False).first(),
            'to_currency': Currency.objects.filter(is_deleted=False).first(),
            'from_amount': Decimal('100.00'),
            'delivery_method': 'bank_transfer'
        }
        
        # Create calculator instance
        calculator = CommissionCalculator()
        
        # This should create a TempTransaction and access DeliveryMethod
        result = calculator.get_commission_preview(transaction_data)
        
        print("✅ TempTransaction DeliveryMethod access working!")
        print(f"   Result: {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ DeliveryMethod access failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("🚀 Arena Doviz Commission Fix Test")
    print("=" * 60)
    
    # Test commission calculation
    commission_test = test_commission_calculation()
    
    # Test DeliveryMethod access
    delivery_test = test_delivery_method_access()
    
    print("\n" + "=" * 60)
    print("📊 Test Results:")
    print(f"✅ Commission Calculation: {'PASS' if commission_test else 'FAIL'}")
    print(f"✅ DeliveryMethod Access: {'PASS' if delivery_test else 'FAIL'}")
    
    if commission_test and delivery_test:
        print("\n🎉 All tests passed! Commission calculation errors should be fixed.")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
