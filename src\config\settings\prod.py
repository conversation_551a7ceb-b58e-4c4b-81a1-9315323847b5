"""
Production settings for Arena Doviz Exchange Accounting System.
"""

from .base import *
import os

# Security settings for production
DEBUG = False

# Disable debug toolbar in production
DEBUG_TOOLBAR_CONFIG = {
    'SHOW_TOOLBAR_CALLBACK': lambda request: False,
}

# Remove debug toolbar from installed apps and middleware if present
INSTALLED_APPS = [app for app in INSTALLED_APPS if 'debug_toolbar' not in app]
MIDDLEWARE = [mw for mw in MIDDLEWARE if 'debug_toolbar' not in mw]

# Add WhiteNoise for static file serving in production
MIDDLEWARE.insert(1, 'whitenoise.middleware.WhiteNoiseMiddleware')

# Add cross-origin policy middleware for production testing
MIDDLEWARE.insert(0, 'apps.core.middleware.CrossOriginPolicyMiddleware')

# CORS and Cross-Origin Policy Configuration
CORS_ALLOW_ALL_ORIGINS = True  # For testing - restrict in production
CORS_ALLOW_CREDENTIALS = True

# Cross-Origin-Opener-Policy and related headers
SECURE_CROSS_ORIGIN_OPENER_POLICY = None  # Disable for HTTP testing
SECURE_REFERRER_POLICY = None  # Disable for HTTP testing

# Security headers for production testing
SECURE_SSL_REDIRECT = False  # Keep False for HTTP testing
SECURE_HSTS_SECONDS = 0  # Disable HSTS for HTTP testing
SECURE_HSTS_INCLUDE_SUBDOMAINS = False
SECURE_HSTS_PRELOAD = False
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'SAMEORIGIN'

# Production hosts - allow access from any device on network
ALLOWED_HOSTS = [
    '*************',  # Production server IP
    'arena-doviz.com',  # Production domain (if applicable)
    'www.arena-doviz.com',  # WWW subdomain (if applicable)
    'localhost',  # For local testing
    '127.0.0.1',  # For local testing
    '0.0.0.0',  # For server binding
    'testserver',  # For testing
    '*',  # Allow all hosts for testing (remove in production)
]

# Database for production - PostgreSQL recommended
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.postgresql',
        'NAME': os.environ.get('DB_NAME', 'arena_doviz_prod'),
        'USER': os.environ.get('DB_USER', 'arena_user'),
        'PASSWORD': os.environ.get('DB_PASSWORD', ''),
        'HOST': os.environ.get('DB_HOST', 'localhost'),
        'PORT': os.environ.get('DB_PORT', '5432'),
        'CONN_MAX_AGE': 60,
        'OPTIONS': {
            'sslmode': 'prefer',
        },
    }
}

# Fallback to SQLite if PostgreSQL not configured
if not os.environ.get('DB_PASSWORD'):
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': os.path.join(BASE_DIR, 'db_production.sqlite3'),
            'CONN_MAX_AGE': 0,  # Don't persist connections to avoid locking
            'OPTIONS': {
                'timeout': 30,  # 30 seconds timeout for database operations
            },
        }
    }
#     'CONN_MAX_AGE': 60,
#     'OPTIONS': {
#         'sslmode': 'require',
#     } if os.environ.get('DB_SSL', 'false').lower() == 'true' else {},
# })

# Static files for production - use consistent path format with base settings
STATIC_ROOT = BASE_DIR / 'staticfiles'
MEDIA_ROOT = BASE_DIR / 'media'

# Static files configuration for production with WhiteNoise
STATICFILES_STORAGE = 'whitenoise.storage.CompressedManifestStaticFilesStorage'

# WhiteNoise configuration
WHITENOISE_USE_FINDERS = True
WHITENOISE_AUTOREFRESH = True  # Only for development/testing

# Fix MIME types for static files
import mimetypes
mimetypes.add_type('application/javascript', '.js')
mimetypes.add_type('text/css', '.css')
mimetypes.add_type('application/json', '.json')
mimetypes.add_type('image/svg+xml', '.svg')
mimetypes.add_type('font/woff', '.woff')
mimetypes.add_type('font/woff2', '.woff2')

# Ensure static files are served in production
STATICFILES_FINDERS = [
    'django.contrib.staticfiles.finders.FileSystemFinder',
    'django.contrib.staticfiles.finders.AppDirectoriesFinder',
]

# Force Django to serve static files in production for testing
# In real production, use nginx or Apache
SERVE_STATIC_FILES = True

# Encryption key configuration
ARENA_ENCRYPTION_KEY = os.environ.get('ARENA_ENCRYPTION_KEY')
if not ARENA_ENCRYPTION_KEY:
    import warnings
    warnings.warn(
        "ARENA_ENCRYPTION_KEY environment variable not set. "
        "This is required for production deployment. "
        "Generate a key using: python -c 'from cryptography.fernet import Fernet; print(Fernet.generate_key().decode())'"
    )

# Security settings
# Note: For production deployment with HTTPS, set ARENA_USE_HTTPS=true environment variable
USE_HTTPS = os.getenv('ARENA_USE_HTTPS', 'false').lower() == 'true'

SECURE_SSL_REDIRECT = USE_HTTPS
SECURE_PROXY_SSL_HEADER = ('HTTP_X_FORWARDED_PROTO', 'https')
SECURE_HSTS_SECONDS = 31536000 if USE_HTTPS else 0
SECURE_HSTS_INCLUDE_SUBDOMAINS = USE_HTTPS
SECURE_HSTS_PRELOAD = USE_HTTPS
SECURE_CONTENT_TYPE_NOSNIFF = True
SECURE_BROWSER_XSS_FILTER = True
X_FRAME_OPTIONS = 'SAMEORIGIN'

# Session security
SESSION_COOKIE_SECURE = USE_HTTPS
SESSION_COOKIE_HTTPONLY = True
CSRF_COOKIE_SECURE = USE_HTTPS

# Additional security headers
SECURE_REFERRER_POLICY = 'strict-origin-when-cross-origin'
SESSION_COOKIE_SAMESITE = 'Lax'  # Changed from Strict to Lax
CSRF_COOKIE_SECURE = USE_HTTPS  # Fixed: should use USE_HTTPS
CSRF_COOKIE_HTTPONLY = True
CSRF_COOKIE_SAMESITE = 'Lax'  # Changed from Strict to Lax

# Email configuration for production
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = os.environ.get('EMAIL_HOST', 'smtp.gmail.com')
EMAIL_PORT = int(os.environ.get('EMAIL_PORT', '587'))
EMAIL_USE_TLS = os.environ.get('EMAIL_USE_TLS', 'True').lower() == 'true'
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER', '')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD', '')
DEFAULT_FROM_EMAIL = os.environ.get('DEFAULT_FROM_EMAIL', '<EMAIL>')

# Logging for production - Use local directory
log_dir = os.path.join(BASE_DIR, 'logs')
os.makedirs(log_dir, exist_ok=True)

LOGGING['handlers']['file']['filename'] = os.path.join(log_dir, 'arena_doviz.log')
LOGGING['handlers']['file']['level'] = 'INFO'  # More verbose for testing
LOGGING['root']['level'] = 'INFO'

# Additional logging handler for errors
LOGGING['handlers']['error_file'] = {
    'level': 'ERROR',
    'class': 'logging.FileHandler',
    'filename': os.path.join(log_dir, 'errors.log'),
    'formatter': 'verbose',
}

LOGGING['loggers']['django']['handlers'].append('error_file')
LOGGING['loggers']['apps']['handlers'].append('error_file')

# CORS settings for production - Allow all origins for testing
CORS_ALLOW_ALL_ORIGINS = True  # For testing - restrict this in production
CORS_ALLOW_CREDENTIALS = True

# Production-specific Arena Doviz settings
ARENA_DOVIZ.update({
    'DEBUG_MODE': False,
    'MOCK_WHATSAPP': False,
    'ENABLE_TEST_DATA': False,
    'BACKUP_ENABLED': True,
    'AUDIT_LOG_RETENTION_DAYS': 365,
    'MAX_UPLOAD_SIZE': 10 * 1024 * 1024,  # 10MB
})

# Cache configuration for production - Use in-memory cache for simplicity
CACHES = {
    'default': {
        'BACKEND': 'django.core.cache.backends.locmem.LocMemCache',
        'LOCATION': 'arena-doviz-cache',
        'TIMEOUT': 300,
        'OPTIONS': {
            'MAX_ENTRIES': 1000,
        }
    }
}
